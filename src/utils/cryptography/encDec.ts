import * as crypto from 'crypto';
import { ObjUnknownI } from '@interfaces/common/data';
import { ENV } from '@consts/common/env';

export const encryptJSON = (data: ObjUnknownI): string => {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv('aes-256-cbc', ENV.ENCRYPTION_SECRET_KEY, iv);
  let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'base64');
  encrypted += cipher.final('base64');
  return `${iv.toString('base64')}:${encrypted}`;
};
export const decryptJSON = (encryptedData: string): ObjUnknownI => {
  const [iv, data] = encryptedData.split(':');
  const decipher = crypto.createDecipheriv('aes-256-cbc', ENV.ENCRYPTION_SECRET_KEY, Buffer.from(iv, 'base64'));
  let decrypted = decipher.update(data, 'base64', 'utf8');
  decrypted += decipher.final('utf8');
  return JSON.parse(decrypted);
};
