import type { SortOrderI } from '@interfaces/common/data';
import { MasterAndRawDataI } from '@interfaces/master/common';
import { IdTypeI } from '@schemas/common/common';

export const sortArrayByString = <T>(arr: T[], key: keyof T, order: SortOrderI): T[] =>
  arr.sort(
    order === 'asc'
      ? (a, b) => String(a[key]).localeCompare(String(b[key]))
      : (a, b) => String(b[key]).localeCompare(String(a[key])),
  );
export const uniqueArrayObj = <T extends { id?: string; [key: string]: unknown }>(data: T[]): T[] => {
  const ids = new Set<string>();
  const filteredArray = data.filter((item) => {
    if (!ids.has(item.id)) {
      ids.add(item.id);
      return true;
    }
    return false;
  });
  return filteredArray;
};
export const separateMasterAndRawData = (data: IdTypeI[]): MasterAndRawDataI => {
  data = uniqueArrayObj(data);
  const result = data.reduce(
    (acc, curr) => {
      if (curr.dataType === 'master') {
        acc.master.push(curr.id);
      } else if (curr.dataType === 'raw') {
        acc.rawData.push(curr.id);
      }
      return acc;
    },
    { master: [], rawData: [] } as MasterAndRawDataI,
  );
  return result;
};
