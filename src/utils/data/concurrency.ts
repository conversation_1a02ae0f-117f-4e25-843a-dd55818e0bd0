export const processInBatches = async <T>(
  inputs: T[],
  callbackFn: (param: T) => Promise<unknown>,
  batchSize: number = 20,
) => {
  const output: unknown[] = [];
  for (let index = 0; index < inputs.length; index += batchSize) {
    const batchInput = inputs.slice(index, index + batchSize);
    const batchResult = await Promise.all(batchInput.map((inputItem) => callbackFn(inputItem)));
    output.push(...batchResult);
  }
  return output;
};
