import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { EitherOr } from '@interfaces/common/data';
import { AppVersion, Prisma } from '@prisma/postgres';

export const AppVersionModule = {
  fetchByIdVersionPlatform: async (
    filters: EitherOr<Prisma.AppVersionWhereUniqueInput, 'id' | 'versionNo' | 'platformId'>,
    select: Prisma.AppVersionSelect = {
      id: true,
      platformId: true,
      isActive: true,
    },
  ): Promise<AppVersion> => {
    const appVersionResult = await prismaPG.appVersion.findFirst({
      select,
      where: filters as Prisma.AppVersionWhereUniqueInput,
    });
    if (!appVersionResult) {
      throw new AppError('APPVN001');
    } else if (!appVersionResult.isActive) {
      throw new AppError('APPVN002');
    }
    return appVersionResult;
  },
};
