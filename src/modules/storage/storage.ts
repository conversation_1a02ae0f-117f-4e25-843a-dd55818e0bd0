import AppError from '@classes/AppError';
import s3 from '@config/s3';
import { ACLI, FileExtensionI, FileTypeE, FileTypeValueI } from '@consts/storage/storage';
import { UploadFileBulkI, UploadFileOneI } from '@interfaces/storage/storage';
import { PresignedURLItemI, PresignedURLItemResultI, PresignedURLsI } from '@schemas/storage/storage';
import { processInBatches } from '@utils/data/concurrency';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { ENV } from '@consts/common/env';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
export const StorageModule = {
  presignedURL: async ({ folder, extension }: PresignedURLItemI): Promise<PresignedURLItemResultI> => {
    switch (folder) {
      case 'POST':
        if (!(['webp', 'jpeg', 'jpg'] as FileExtensionI[]).includes(extension)) {
          throw new AppError('STRG002');
        }
        break;
      case 'AVATAR':
        if (!(['webp', 'jpeg', 'jpg'] as FileExtensionI[]).includes(extension)) {
          throw new AppError('STRG002');
        }
        break;
      case 'CERTIFICATION':
        if (!(['webp', 'jpeg', 'jpg', 'pdf'] as FileExtensionI[]).includes(extension)) {
          throw new AppError('STRG002');
        }
        break;
      case 'DOCUMENTATION':
        if (!(['webp', 'jpeg', 'jpg', 'pdf'] as FileExtensionI[]).includes(extension)) {
          throw new AppError('STRG002');
        }
        break;
      default:
        throw new AppError('STRG002');
    }
    const Bucket = folder;
    const fileName = `${crypto.randomUUID()}.${extension}`;

    const command = new PutObjectCommand({
      Bucket,
      Key: fileName,
      ContentType: FileTypeE[extension].mime,
      ACL: 'public-read',
    });
    const uploadUrl = await getSignedUrl(s3, command, { expiresIn: ENV.DO_SIGNED_URL_EXPIRY_S });
    const accessUrl = `${ENV.DO_SPACES_CDN_ENDPOINT}/${folder}/${fileName}`;
    const result: PresignedURLItemResultI = {
      extension,
      uploadUrl,
      accessUrl,
    };
    return result;
  },
  presignedURLsBulk: async ({ extensions, folder }: PresignedURLsI): Promise<PresignedURLItemResultI[]> => {
    const presignedURsResult: PresignedURLItemResultI[] = await Promise.all(
      extensions.map((extension) => StorageModule.presignedURL({ extension, folder })),
    );
    return presignedURsResult;
  },
  uploadFilesBulk: async ({ files, parentFolder }: UploadFileBulkI): Promise<string[]> => {
    const output: string[] = (await processInBatches(
      files.map((file) => ({ file, parentFolder: parentFolder }) as UploadFileOneI),
      StorageModule.uploadFileOne,
      20,
    )) as string[];
    return output;
  },
  /**
   *
   * @param param
   *
   * File path (URL):  /<parentFolder>/<fileName>.<fileExtension>
   */
  uploadFileOne: async ({ file, parentFolder }: UploadFileOneI): Promise<string> => {
    try {
      switch (parentFolder) {
        case 'DOCUMENTATION': {
          switch (file.mimetype) {
            case FileTypeE.pdf.mime: {
              break;
            }
            case FileTypeE.webp.mime: {
              break;
            }
            default: {
              throw new AppError('STRG002');
            }
          }
          break;
        }
        case 'CERTIFICATION': {
          switch (file.mimetype) {
            case FileTypeE.pdf.mime: {
              break;
            }
            case FileTypeE.webp.mime: {
              break;
            }
            default: {
              throw new AppError('STRG002');
            }
          }
          break;
        }
        case 'AVATAR': {
          switch (file.mimetype) {
            case FileTypeE.webp.mime: {
              break;
            }
            default: {
              throw new AppError('STRG002');
            }
          }
          break;
        }
        case 'POST': {
          switch (file.mimetype) {
            case FileTypeE.webp.mime: {
              break;
            }
            default: {
              throw new AppError('STRG002');
            }
          }
          break;
        }
      }
      const fileType: FileTypeValueI = FileTypeE[file.mimetype];
      const filePath = `${parentFolder}/${crypto.randomUUID()}.${fileType.extension}`;
      const body = await file.toBuffer();
      await s3.send(
        new PutObjectCommand({
          Bucket: ENV.DO_SPACES,
          Key: filePath,
          ACL: 'public-read' as ACLI,
          Body: body,
          ContentType: fileType.mime,
        }),
      );
      const url = `${ENV.DO_SPACES_ENDPOINT}/${filePath}`;
      return url;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('STRG001');
    }
  },
};
