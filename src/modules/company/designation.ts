import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { IdNameI } from '@consts/master/common';
import { ObjUnknownI } from '@interfaces/common/data';
import { PostgresTxnI } from '@interfaces/common/db';
import {
  DesignationClientI,
  DesignationModuleFetchsertParamsI,
  DesignationRawQueryFetchForClientResultI,
} from '@interfaces/company/designation';
import { IdTypeI, PaginationI } from '@schemas/common/common';
export const DesignationModule = {
  fetchById: async (filters: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<DesignationClientI> => {
    const select: IdNameI = { id: true, name: true };
    if (filters.dataType === 'raw') {
      const designationRawDataResult = await txn.designationRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...designationRawDataResult,
        dataType: 'raw',
      } as DesignationClientI;
    } else if (filters.dataType === 'master') {
      const designationAlternativeResult = await txn.designationAlternative.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(designationAlternativeResult as ObjUnknownI),
        dataType: 'master',
      } as DesignationClientI;
    }
    throw new AppError('DSG001');
  },
  fetchForClient: async (
    name?: string,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ data: DesignationRawQueryFetchForClientResultI[]; total: number }> => {
    name = name.trim().toLowerCase();
    const offset = pagination.page * pagination.pageSize;
    const limit = pagination.pageSize;

    const DesignationRawQueryResult: DesignationRawQueryFetchForClientResultI[] = await prismaPG.$queryRaw<
      DesignationRawQueryFetchForClientResultI[]
    >`
        WITH master_designations as (
          SELECT
            designation."id",
            designation."name" AS name,
            'master' AS "dataType"
          FROM "company"."DesignationAlternative" designation
          WHERE
            designation."name" ILIKE ${name + '%'}
        ),
        raw_designations as (
          SELECT
            designationRawData."id",
            designationRawData."name" AS name,
            'raw' AS "dataType"
          FROM "rawData"."DesignationRawData" designationRawData
          WHERE
            designationRawData."name" ILIKE ${name + '%'}
        )
        SELECT * FROM (
        SELECT * FROM master_designations
        UNION ALL
        SELECT * FROM raw_designations
        ) combined_results
        ORDER BY name ASC
        LIMIT ${limit} OFFSET ${offset}
        `;

    const totalResult = await prismaPG.$queryRaw<{ total: number }[]>`
              WITH master_designation AS (
                SELECT designation."id"
                FROM "company"."DesignationAlternative" designation
                WHERE
                  designation."name" ILIKE ${name + '%'}
              ),
              raw_designation AS (
                SELECT designationRawData."id"
                FROM "rawData"."DesignationRawData" designationRawData
                WHERE
                  designationRawData."name" ILIKE ${name + '%'}
              ),
              combined AS (
                SELECT * FROM master_designation
                UNION ALL
                SELECT * FROM raw_designation
              )
              SELECT COUNT(*)::INTEGER AS total FROM combined
            `;

    return {
      data: DesignationRawQueryResult,
      total: totalResult?.[0]?.total || 0,
    };
  },
  fetchsert: async (params: DesignationModuleFetchsertParamsI): Promise<DesignationClientI> => {
    const [designationResult, designationRawDataResult] = await Promise.all([
      prismaPG.designationAlternative.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
        },
        select: { id: true, name: true },
      }),
      prismaPG.designationRawData.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
        },
        select: { id: true, name: true },
      }),
    ]);
    if (designationResult) {
      return { ...designationResult, dataType: 'master' as DBDataTypeI } as DesignationClientI;
    } else if (designationRawDataResult) {
      return { ...designationRawDataResult, dataType: 'raw' as DBDataTypeI } as DesignationClientI;
    }
    const result = await prismaPG.designationRawData.create({ data: params });
    if (!result?.id) {
      throw new AppError('DSG002');
    }
    return { ...result, dataType: 'raw' as DBDataTypeI } as DesignationClientI;
  },
};
