import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { ObjUnknownI } from '@interfaces/common/data';
import { PostgresTxnI } from '@interfaces/common/db';
import type {
  EntityClientI,
  EntityModuleFetchsertParamsI,
  EntityRawQueryFetchForClientResultI,
} from '@interfaces/company/entity';
import { EntityTypeE, Prisma } from '@prisma/postgres';
import type { IdTypeI, PaginationI } from '@schemas/common/common';

export const EntityModule = {
  fetchById: async (filters: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<EntityClientI> => {
    const select: Prisma.EntitySelect = { id: true, name: true, type: true };
    if (filters.dataType === 'raw') {
      const entityRawDataResult = await txn.entityRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(entityRawDataResult as ObjUnknownI),
        dataType: 'raw',
      } as EntityClientI;
    } else if (filters.dataType === 'master') {
      const entityResult = await txn.entity.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(entityResult as ObjUnknownI),
        dataType: 'master',
      } as EntityClientI;
    }
    throw new AppError('ORG001');
  },
  fetchForClient: async (
    name?: string,
    type?: EntityTypeE,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ data: EntityRawQueryFetchForClientResultI[]; total: number }> => {
    name = name?.trim()?.toLowerCase() || '';
    const offset = pagination.page * pagination.pageSize;
    const limit = pagination.pageSize;

    const data: EntityRawQueryFetchForClientResultI[] = await prismaPG.$queryRaw<EntityRawQueryFetchForClientResultI[]>`
      WITH master_entity AS (
        SELECT
          entity."id",
          entity."name" AS name,
          entity."type",
          'master' AS "dataType"
        FROM "company"."Entity" entity
        WHERE
          entity."name" ILIKE ${name + '%'}
          ${type ? Prisma.sql`AND entity."type" = ${type}::"company"."EntityTypeE"` : Prisma.empty}
      ),
      raw_entity AS (
        SELECT
          entityRawData."id",
          entityRawData."name" AS name,
          entityRawData."type",
          'raw' AS "dataType"
        FROM "rawData"."EntityRawData" entityRawData
        WHERE
          entityRawData."name" ILIKE ${name + '%'}
          ${type ? Prisma.sql`AND entityRawData."type" = ${type}::"company"."EntityTypeE"` : Prisma.empty}
      ),
      combined AS (
        SELECT * FROM master_entity
        UNION ALL
        SELECT * FROM raw_entity
      )
      SELECT * FROM combined
      ORDER BY name ASC
      LIMIT ${limit} OFFSET ${offset}
    `;

    const totalResult = await prismaPG.$queryRaw<{ total: number }[]>`
      WITH master_entity AS (
        SELECT entity."id"
        FROM "company"."Entity" entity
        WHERE
          entity."name" ILIKE ${name + '%'}
          ${type ? Prisma.sql`AND entity."type" = ${type}::"company"."EntityTypeE"` : Prisma.empty}
      ),
      raw_entity AS (
        SELECT entityRawData."id"
        FROM "rawData"."EntityRawData" entityRawData
        WHERE
          entityRawData."name" ILIKE ${name + '%'}
          ${type ? Prisma.sql`AND entityRawData."type" = ${type}::"company"."EntityTypeE"` : Prisma.empty}
      ),
      combined AS (
        SELECT * FROM master_entity
        UNION ALL
        SELECT * FROM raw_entity
      )
      SELECT COUNT(*)::INTEGER AS total FROM combined
    `;

    return {
      data,
      total: totalResult?.[0]?.total || 0,
    };
  },
  fetchsert: async (params: EntityModuleFetchsertParamsI): Promise<EntityClientI> => {
    const [entityResult, entityRawDataResult] = await Promise.all([
      prismaPG.entity.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
          type: params.type,
        },
        select: { id: true, name: true, type: true },
      }),
      prismaPG.entityRawData.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
          type: params.type,
        },
        select: { id: true, name: true, type: true },
      }),
    ]);
    if (entityResult) {
      return { ...entityResult, dataType: 'master' as DBDataTypeI } as EntityClientI;
    } else if (entityRawDataResult) {
      return { ...entityRawDataResult, dataType: 'raw' as DBDataTypeI } as EntityClientI;
    }
    const result = await prismaPG.entityRawData.create({ data: params });
    if (!result?.id) {
      throw new AppError('ORG002');
    }
    return { ...result, dataType: 'raw' as DBDataTypeI } as unknown as EntityClientI;
  },
};
