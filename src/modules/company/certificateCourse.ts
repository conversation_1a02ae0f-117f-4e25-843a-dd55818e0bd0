import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { CertificateCourseTypeI } from '@consts/company/certificateCourse';
import { ObjUnknownI } from '@interfaces/common/data';
import {
  CertificateCourseClientI,
  CertificateCourseModuleFetchsertParamsI,
} from '@interfaces/company/certificateCourse';
import { Prisma } from '@prisma/postgres';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import { sortArrayByString } from '@utils/data/array';

export const CertificateCourseModule = {
  fetchById: async (
    filters: IdTypeI,
    select: Prisma.CertificateCourseSelect = { id: true, name: true, type: true },
    _isThrowingError: boolean = true,
  ): Promise<CertificateCourseClientI> => {
    if (filters.dataType === 'raw') {
      const certificateCourseRawDataResult = await prismaPG.certificateCourseRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(certificateCourseRawDataResult as ObjUnknownI),
        dataType: 'raw',
      } as CertificateCourseClientI;
    } else if (filters.dataType === 'master') {
      const certificateCourseResult = await prismaPG.certificateCourse.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(certificateCourseResult as ObjUnknownI),
        dataType: 'master',
      } as CertificateCourseClientI;
    }

    if (_isThrowingError) {
      throw new AppError('ORG001');
    }
  },
  fetchForClient: async (
    name?: string,
    type?: CertificateCourseTypeI,
    pagination: PaginationI = PAGINATION,
    orderBy: Prisma.CertificateCourseOrderByWithRelationInput = { name: 'asc' },
    select: Prisma.CertificateCourseSelect = { id: true, name: true, type: true },
  ): Promise<{ data: CertificateCourseClientI[]; total: number }> => {
    name = name.trim().toLowerCase();
    let certificateCoursesResult: CertificateCourseClientI[] = [];
    const filters: Prisma.CertificateCourseWhereInput = {};
    if (name?.length) {
      filters.name = {
        startsWith: name,
        mode: 'insensitive',
      };
    }
    if (type?.length) {
      filters.type = type;
    }
    if (name?.length) {
      filters.name = {
        startsWith: name,
        mode: 'insensitive',
      };
    }
    const [certificateCourseResultTemp, certificateCourseRawDataTemp] = await Promise.all([
      prismaPG.certificateCourse.findMany({
        where: filters,
        orderBy,
        select,
        skip: pagination.page,
        take: pagination.pageSize,
      }),
      name?.length
        ? prismaPG.certificateCourseRawData.findMany({
            where: {
              name,
            },
            skip: pagination.page,
            take: pagination.pageSize,
            orderBy: { name: 'asc' },
            select: { id: true, name: true, type: true },
          })
        : [],
    ]);

    if (certificateCourseResultTemp?.length) {
      certificateCoursesResult.push(
        ...certificateCourseResultTemp.map((item) => ({ ...item, dataType: 'master' }) as CertificateCourseClientI),
      );
    }
    if (certificateCourseRawDataTemp?.length) {
      certificateCoursesResult.push(
        ...certificateCourseRawDataTemp.map((item) => ({ ...item, type: 'raw' }) as CertificateCourseClientI),
      );
    }
    if (certificateCoursesResult?.length) {
      certificateCoursesResult = sortArrayByString(certificateCoursesResult, 'name', 'asc');
    }

    const totalResult = await prismaPG.$queryRaw<{ total: number }[]>`
          WITH master_certificate_course AS (
            SELECT certificate."id"
            FROM "company"."CertificateCourse" certificate
            WHERE
              certificate."name" ILIKE ${name + '%'}
              ${type ? Prisma.sql`AND certificate."type" = ${type}::"company"."CertificateCourseTypeE"` : Prisma.empty}
          ),
          raw_certificate_course AS (
            SELECT certificateRawData."id"
            FROM "rawData"."CertificateCourseRawData" certificateRawData
            WHERE
              certificateRawData."name" ILIKE ${name + '%'}
              ${type ? Prisma.sql`AND certificateRawData."type" = ${type}::"company"."CertificateCourseTypeE"` : Prisma.empty}
          ),
          combined AS (
            SELECT * FROM master_certificate_course
            UNION ALL
            SELECT * FROM raw_certificate_course
          )
          SELECT COUNT(*)::INTEGER AS total FROM combined
        `;

    return {
      data: certificateCoursesResult,
      total: totalResult?.[0]?.total || 0,
    };
  },
  fetchsert: async (params: CertificateCourseModuleFetchsertParamsI): Promise<CertificateCourseClientI> => {
    const [certificateCourseResult, certificateCourseRawDataResult] = await Promise.all([
      prismaPG.certificateCourse.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
          type: params.type,
        },
        select: { id: true, name: true, type: true },
      }),
      prismaPG.certificateCourseRawData.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
          type: params.type,
        },
        select: { id: true, name: true, type: true },
      }),
    ]);
    if (certificateCourseResult) {
      return { ...certificateCourseResult, dataType: 'master' as DBDataTypeI } as CertificateCourseClientI;
    } else if (certificateCourseRawDataResult) {
      return { ...certificateCourseRawDataResult, dataType: 'raw' as DBDataTypeI } as CertificateCourseClientI;
    }
    const result = await prismaPG.certificateCourseRawData.create({ data: params });
    if (!result?.id) {
      throw new AppError('CRTCR002');
    }
    return { ...result, dataType: 'raw' as DBDataTypeI } as CertificateCourseClientI;
  },
};
