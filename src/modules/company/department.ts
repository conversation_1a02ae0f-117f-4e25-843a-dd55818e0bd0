import AppError from '@classes/AppError';
import { PAGINATION } from '@consts/common/pagination';
import { IdTypeI } from '@schemas/common/common';
import { prismaPG } from '@config/db';
import { DepartmentClientI, DepartmentModuleFetchsertParamsI,DepartmentNestedClientI } from '@interfaces/company/department';
import { DBDataTypeI } from '@consts/common/data';
import { IdNameI } from '@consts/master/common';
import { ObjUnknownI , TotalDataI } from '@interfaces/common/data';

export const DepartmentModule = {
  fetchById: async (
    filters: IdTypeI,
    select: IdNameI = { id: true, name: true },
    _isThrowingError: boolean = true,
  ): Promise<DepartmentClientI> => {
    if (filters.dataType === 'raw') {
      const departmentRawDataResult = await prismaPG.departmentRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(departmentRawDataResult as ObjUnknownI),
        dataType: 'raw',
      } as DepartmentClientI;
    } else if (filters.dataType === 'master') {
      const departmentAlternativeResult = await prismaPG.departmentAlternative.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...(departmentAlternativeResult as ObjUnknownI),
        dataType: 'master',
      } as DepartmentClientI;
    }

    if (_isThrowingError) {
      throw new AppError('DEP001');
    }
  },
  fetchForClient: async (name?: string, { page, pageSize } = PAGINATION): Promise<TotalDataI<DepartmentNestedClientI>> => {
    name = name?.trim()?.toLowerCase();


    const [departmentsResultTemp, departmentsTotalResult] = await Promise.all([
      prismaPG.$queryRaw<DepartmentNestedClientI[]>`
          SELECT *
          FROM
          (
            (SELECT
            da."id",
            da."name",
            'master' AS "dataType"
            FROM
            "company"."DepartmentAlternative" da
            WHERE
            da."name" ILIKE ${name + '%'})
            UNION ALL
            (SELECT
            drw."id",
            drw."name",
            'raw' AS "dataType"
            FROM
            "rawData"."DepartmentRawData" drw
            WHERE
            drw."name" ILIKE ${name + '%'})
          )
          ORDER BY "name" ASC
          OFFSET ${page * pageSize}
          LIMIT ${pageSize}
      `,
      prismaPG.$queryRaw<{ total: number }[]>`
              WITH master_department AS (
                SELECT 1
                FROM "company"."DepartmentAlternative" department
                WHERE
                  department."name" ILIKE ${name + '%'}
              ),
              raw_department AS (
                SELECT 1
                FROM "rawData"."DepartmentRawData" departmentRawData
                WHERE
                  departmentRawData."name" ILIKE ${name + '%'}
              ),
              combined AS (
                SELECT * FROM master_department
                UNION ALL
                SELECT * FROM raw_department
              )
              SELECT COUNT(*)::INTEGER AS total FROM combined
            `,
    ]);


    return {
      data: departmentsResultTemp,
      total: Number(departmentsTotalResult[0]?.total || 0),
    };
  },
  fetchsert: async (params: DepartmentModuleFetchsertParamsI): Promise<DepartmentClientI> => {
     const name = params.name.trim().toLowerCase();
   const existingDepartment = await prismaPG.$queryRaw<DepartmentClientI[]>`
      WITH master_dept AS (
        SELECT
          id,
          name,
          'master' AS "dataType",
          1 AS priority
        FROM "company"."DepartmentAlternative"
        WHERE LOWER(name) = ${name}
        LIMIT 1
      ),
      raw_dept AS (
        SELECT
          id,
          name,
          'raw' AS "dataType",
          2 AS priority
        FROM "rawData"."DepartmentRawData"
        WHERE LOWER(name) = ${name}
        AND NOT EXISTS (SELECT 1 FROM master_dept)
        LIMIT 1
      )
      SELECT id, name, "dataType" FROM (
        SELECT * FROM master_dept
        UNION ALL
        SELECT * FROM raw_dept
      ) AS combined
      LIMIT 1
    `;

    if (existingDepartment.length > 0) {
      return existingDepartment[0];
    }

  
    const result = await prismaPG.departmentRawData.create({
      data: {
        name: params.name.trim().toLowerCase(),
        ...params
      }
    });

    if (!result?.id) {
      throw new AppError('DEP002');
    }

    return {
      id: result.id,
      name: result.name,
      dataType: 'raw' as DBDataTypeI
    } as DepartmentClientI;
  },
 count: async (masterDepartments: string[], rawDataDepartments: string[]) => {
    masterDepartments = Array.from(new Set<string>(masterDepartments));
    rawDataDepartments = Array.from(new Set<string>(rawDataDepartments));
    const [countMasterDepartments, countRawDataDepartments] = await Promise.all([
      masterDepartments?.length
        ? prismaPG.departmentAlternative.count({
            where: {
              id: {
                in: masterDepartments,
              },
            },
          })
        : 0,
      rawDataDepartments?.length
        ? prismaPG.departmentRawData.count({
            where: {
              id: {
                in: rawDataDepartments,
              },
            },
          })
        : 0,
    ]);
    return { countMasterDepartments, countRawDataDepartments };
  },
};
