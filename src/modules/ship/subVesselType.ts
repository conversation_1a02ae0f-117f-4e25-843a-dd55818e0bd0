import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { IdNameI } from '@consts/master/common';
import { PostgresTxnI } from '@interfaces/common/db';
import { SubVesselTypeNestedClientI } from '@interfaces/ship/subVesselType';
import { Prisma } from '@prisma/postgres';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import type { SubVesselTypeFetchForClientI, SubVesselTypeFetchsertI } from '@schemas/ship/subVesselType';
import { sortArrayByString } from '@utils/data/array';

export const SubVesselTypeModule = {
  fetchById: async (filters: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<SubVesselTypeNestedClientI> => {
    const select: IdNameI = { id: true, name: true };
    if (filters.dataType === 'raw') {
      const subVesselTypeRawDataResult = await txn.subVesselTypeRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...subVesselTypeRawDataResult,
        dataType: 'raw',
      } as SubVesselTypeNestedClientI;
    } else if (filters.dataType === 'master') {
      const subVesselTypeResult = await txn.subVesselType.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...subVesselTypeResult,
        dataType: 'master',
      } as SubVesselTypeNestedClientI;
    }
    throw new AppError('MVSLTP001');
  },
  fetchForClient: async (
    filtersP: SubVesselTypeFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ data: SubVesselTypeNestedClientI[]; total: number }> => {
    filtersP.search = filtersP.search.trim().toLowerCase();
    let subVesselTypesResult: SubVesselTypeNestedClientI[] = [];
    const filters: Prisma.SubVesselTypeWhereInput = {
      name: {
        startsWith: filtersP.search,
        mode: 'insensitive',
      },
    };
    const rawDataFilters: Prisma.SubVesselTypeRawDataWhereInput = {
      name: {
        startsWith: filtersP.search,
        mode: 'insensitive',
      },
    };
    const [subVesselTypeResultTemp, subVesselTypeRawDataTemp, subVesselTypeResultTotal, subVesselTypeRawDataTotal] =
      await Promise.all([
        prismaPG.subVesselType.findMany({
          where: filters,
          skip: pagination.page,
          take: pagination.pageSize,
          orderBy: { name: 'asc' },
          select: { id: true, name: true },
        }),
        prismaPG.subVesselTypeRawData.findMany({
          where: rawDataFilters,
          skip: pagination.page,
          take: pagination.pageSize,
          orderBy: { name: 'asc' },
          select: { id: true, name: true },
        }),
        prismaPG.subVesselType.count({ where: filters }),
        prismaPG.subVesselTypeRawData.count({ where: rawDataFilters }),
      ]);

    if (subVesselTypeResultTemp?.length) {
      subVesselTypesResult.push(
        ...subVesselTypeResultTemp.map(
          (subVesselType) => ({ ...subVesselType, dataType: 'master' }) as SubVesselTypeNestedClientI,
        ),
      );
    }

    if (subVesselTypeRawDataTemp?.length) {
      subVesselTypesResult.push(
        ...subVesselTypeRawDataTemp.map(
          (subVesselType) => ({ ...subVesselType, dataType: 'raw' }) as SubVesselTypeNestedClientI,
        ),
      );
    }
    if (subVesselTypesResult?.length) {
      subVesselTypesResult = sortArrayByString(subVesselTypesResult, 'name', 'asc');
    }
    return {
      data: subVesselTypesResult,
      total: subVesselTypeResultTotal + subVesselTypeRawDataTotal,
    };
  },
  fetchsert: async ({ name, mainVesselTypeId }: SubVesselTypeFetchsertI): Promise<SubVesselTypeNestedClientI> => {
     name = name?.toLowerCase()?.trim();
     const subVesselTypeRawQueryResult = await prismaPG.$queryRaw<SubVesselTypeNestedClientI[]>`
      SELECT * FROM
      (
        SELECT
          v."id",
          v."name",
          'master' AS "dataType"
        FROM
        "ship"."SubVesselType" v
        WHERE
        v."name" = ${name}
        UNION
        SELECT
          vrw."id",
          vrw."name",
          'raw' AS "dataType"
        FROM
          "rawData"."SubVesselTypeRawData" vrw
        WHERE
          vrw."name" = ${name}
      ) AS combinedResult
      ORDER BY
        combinedResult."dataType" ASC,
        combinedResult."name" ASC
      LIMIT 1
    `;

    if (subVesselTypeRawQueryResult && subVesselTypeRawQueryResult.length > 0) {
      return subVesselTypeRawQueryResult[0];
    }

      const subVesselTypResultTemp = await prismaPG.subVesselTypeRawData.create({
      data: {
        name,
        mainVesselTypeId,
      },
      select: {
        id: true,
        name: true,
      },
    });
    return {
      id: subVesselTypResultTemp.id,
      name,
      dataType: 'raw' as DBDataTypeI,
    } as SubVesselTypeNestedClientI;
  }
};
