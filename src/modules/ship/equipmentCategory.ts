import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { PostgresTxnI } from '@interfaces/common/db';
import { EquipmentCategoryClientI } from '@interfaces/ship/equipmentCategory';
import { Prisma } from '@prisma/postgres';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import type { EquipmentCategoryFetchForClientI, EquipmentCategoryFetchsertI } from '@schemas/ship/equipmentCategory';
import { sortArrayByString } from '@utils/data/array';

export const EquipmentCategoryModule = {
  fetchById: async (filters: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<EquipmentCategoryClientI> => {
    const select: Prisma.EquipmentCategorySelect = { id: true, name: true, hasFuelType: true };
    if (filters.dataType === 'raw') {
      const equipmentCategoryRawDataResult = await txn.equipmentCategoryRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...equipmentCategoryRawDataResult,
        dataType: 'raw',
      } as EquipmentCategoryClientI;
    } else if (filters.dataType === 'master') {
      const equipmentCategoryResult = await txn.equipmentCategory.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...equipmentCategoryResult,
        dataType: 'master',
      } as EquipmentCategoryClientI;
    }
    throw new AppError('EQPCG001');
  },
  fetchForClient: async (
    filtersP: EquipmentCategoryFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ data: EquipmentCategoryClientI[]; total: number }> => {
    filtersP.search = filtersP.search.trim().toLowerCase();
    let equipmentCategorysResult: EquipmentCategoryClientI[] = [];
    const filters: Prisma.EquipmentCategoryWhereInput = {};
    const rawDataFilters: Prisma.EquipmentCategoryRawDataWhereInput = {};
    if (filtersP.search?.length) {
      filters.name = {
        startsWith: filtersP.search,
        mode: 'insensitive',
      };
      rawDataFilters.name = {
        startsWith: filtersP.search,
        mode: 'insensitive',
      };
    }
    const [
      equipmentCategoryResultTemp,
      equipmentCategoryRawDataTemp,
      equipmentCategoryCount,
      equipmentCategoryRawDataCount,
    ] = await Promise.all([
      prismaPG.equipmentCategory.findMany({
        where: filters,
        skip: pagination.page,
        take: pagination.pageSize,
        orderBy: { name: 'asc' },
        select: { id: true, name: true, hasFuelType: true },
      }),
      prismaPG.equipmentCategoryRawData.findMany({
        where: rawDataFilters,
        skip: pagination.page,
        take: pagination.pageSize,
        orderBy: { name: 'asc' },
        select: { id: true, name: true, hasFuelType: true },
      }),
      prismaPG.equipmentCategory.count({ where: filters }),
      prismaPG.equipmentCategoryRawData.count({ where: rawDataFilters }),
    ]);

    if (equipmentCategoryResultTemp?.length) {
      equipmentCategorysResult.push(
        ...equipmentCategoryResultTemp.map(
          (equipmentCategory) => ({ ...equipmentCategory, dataType: 'master' }) as EquipmentCategoryClientI,
        ),
      );
    }

    if (equipmentCategoryRawDataTemp?.length) {
      equipmentCategorysResult.push(
        ...equipmentCategoryRawDataTemp.map(
          (equipmentCategory) => ({ ...equipmentCategory, dataType: 'raw' }) as EquipmentCategoryClientI,
        ),
      );
    }
    if (equipmentCategorysResult?.length) {
      equipmentCategorysResult = sortArrayByString(equipmentCategorysResult, 'name', 'asc');
    }
    return { data: equipmentCategorysResult, total: equipmentCategoryCount + equipmentCategoryRawDataCount };
  },
  fetchsert: async (params: EquipmentCategoryFetchsertI) => {
    const [equipmentCategoryResult, equipmentCategoryRawDataResult] = await Promise.all([
      prismaPG.equipmentCategory.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
        },
        select: { id: true, name: true, hasFuelType: true },
      }),
      prismaPG.equipmentCategoryRawData.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
        },
        select: { id: true, name: true, hasFuelType: true },
      }),
    ]);
    if (equipmentCategoryResult) {
      return { ...equipmentCategoryResult, dataType: 'master' as DBDataTypeI } as EquipmentCategoryClientI;
    } else if (equipmentCategoryRawDataResult) {
      return { ...equipmentCategoryRawDataResult, dataType: 'raw' as DBDataTypeI } as EquipmentCategoryClientI;
    } else {
      const input: Prisma.EquipmentCategoryCreateInput = {
        name: params.name,
        hasFuelType: true,
      };
      const result = await prismaPG.equipmentCategoryRawData.create({
        data: input,
        select: { id: true, name: true, hasFuelType: true },
      });
      if (!result?.id) {
        throw new AppError('EQPCG002');
      }
      return { ...result, dataType: 'raw' as DBDataTypeI } as EquipmentCategoryClientI;
    }
  },
};
