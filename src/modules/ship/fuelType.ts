import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { DBDataTypeI } from '@consts/common/data';
import { PAGINATION } from '@consts/common/pagination';
import { IdNameI } from '@consts/master/common';
import { FuelTypeClientI } from '@interfaces/ship/fuelType';
import { Prisma } from '@prisma/postgres';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import type { FuelTypeFetchForClientI, FuelTypeFetchsertI } from '@schemas/ship/fuelType';
import { sortArrayByString } from '@utils/data/array';

export const FuelTypeModule = {
  fetchById: async (
    filters: IdTypeI,
    select: IdNameI = { id: true, name: true },
    _isThrowingError: boolean = true,
  ): Promise<FuelTypeClientI> => {
    if (filters.dataType === 'raw') {
      const fuelTypeRawDataResult = await prismaPG.fuelTypeRawData.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...fuelTypeRawDataResult,
        dataType: 'raw',
      } as FuelTypeClientI;
    } else if (filters.dataType === 'master') {
      const fuelTypeResult = await prismaPG.fuelType.findFirst({
        where: { id: filters.id },
        select,
      });
      return {
        ...fuelTypeResult,
        dataType: 'master',
      } as FuelTypeClientI;
    }

    if (_isThrowingError) {
      throw new AppError('FULTYP001');
    }
  },
  fetchForClient: async (
    filtersP: FuelTypeFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ data: FuelTypeClientI[]; total: number }> => {
    filtersP.search = filtersP.search.trim().toLowerCase();
    let fuelTypesResult: FuelTypeClientI[] = [];
    const filters: Prisma.FuelTypeWhereInput = {};
    const rawDataFilters: Prisma.FuelTypeRawDataWhereInput = {};
    if (filtersP.search?.length) {
      filters.name = {
        startsWith: filtersP.search,
        mode: 'insensitive',
      };
      rawDataFilters.name = {
        startsWith: filtersP.search,
        mode: 'insensitive',
      };
    }
    const [fuelTypeResultTemp, fuelTypeRawDataTemp, fuelTypeCount, fuelTypeRawDataCount] = await Promise.all([
      prismaPG.fuelType.findMany({
        where: filters,
        skip: pagination.page,
        take: pagination.pageSize,
        orderBy: { name: 'asc' },
        select: { id: true, name: true },
      }),
      prismaPG.fuelTypeRawData.findMany({
        where: rawDataFilters,
        skip: pagination.page,
        take: pagination.pageSize,
        orderBy: { name: 'asc' },
        select: { id: true, name: true },
      }),
      prismaPG.fuelType.count({ where: filters }),
      prismaPG.fuelTypeRawData.count({ where: rawDataFilters }),
    ]);

    if (fuelTypeResultTemp?.length) {
      fuelTypesResult.push(
        ...fuelTypeResultTemp.map((fuelType) => ({ ...fuelType, dataType: 'master' }) as FuelTypeClientI),
      );
    }

    if (fuelTypeRawDataTemp?.length) {
      fuelTypesResult.push(
        ...fuelTypeRawDataTemp.map((fuelType) => ({ ...fuelType, dataType: 'raw' }) as FuelTypeClientI),
      );
    }
    if (fuelTypesResult?.length) {
      fuelTypesResult = sortArrayByString(fuelTypesResult, 'name', 'asc');
    }
    return { data: fuelTypesResult, total: fuelTypeCount + fuelTypeRawDataCount };
  },
  fetchsert: async (params: FuelTypeFetchsertI) => {
    const [fuelTypeResult, fuelTypeRawDataResult] = await Promise.all([
      prismaPG.fuelType.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
        },
        select: { id: true, name: true },
      }),
      prismaPG.fuelTypeRawData.findFirst({
        where: {
          name: {
            equals: params.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
        },
        select: { id: true, name: true },
      }),
    ]);
    if (fuelTypeResult) {
      return { ...fuelTypeResult, dataType: 'master' as DBDataTypeI } as FuelTypeClientI;
    } else if (fuelTypeRawDataResult) {
      return { ...fuelTypeRawDataResult, dataType: 'raw' as DBDataTypeI } as FuelTypeClientI;
    } else {
      const input: Prisma.FuelTypeCreateInput = {
        name: params.name,
      };
      const result = await prismaPG.fuelTypeRawData.create({
        data: input,
        select: { id: true, name: true },
      });
      if (!result?.id) {
        throw new AppError('FULTYP002');
      }
      return { ...result, dataType: 'raw' as DBDataTypeI } as FuelTypeClientI;
    }
  },
};
