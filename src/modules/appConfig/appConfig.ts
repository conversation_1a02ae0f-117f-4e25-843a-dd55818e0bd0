import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import { AuthSessionAppConfigI } from '@interfaces/appConfig/appConfig';
import { ModuleE, Prisma, SubModuleE } from '@prisma/mongodb';
const AppConfigModule = {
  fetchById: async (
    filters: Prisma.AppConfigWhereInput,
    select: Prisma.AppConfigSelect = {
      id: true,
      config: true,
    },
  ): Promise<AuthSessionAppConfigI> => {
    const appConfigResult = await prismaMG.appConfig.findFirst({
      where: filters,
      select,
    });
    if (!appConfigResult) {
      throw new AppError('APPCFG001');
    }
    switch (filters.module) {
      case ModuleE.AUTH: {
        switch (filters.subModule) {
          case SubModuleE.SESSION: {
            return appConfigResult.config as AuthSessionAppConfigI;
          }
        }
        break;
      }
    }
    throw new AppError('APPCFG001');
  },
};
export default AppConfigModule;
