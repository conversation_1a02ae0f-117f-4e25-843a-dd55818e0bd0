import AppError from '@classes/AppError';
import { PAGINATION } from '@consts/common/pagination';
import { Prisma } from '@prisma/postgres';
import { PaginationI } from '@schemas/common/common';
import { sortArrayByString } from '@utils/data/array';
import { prismaPG } from '@config/db';
import { CityNestedClientI } from '@interfaces/master/city';
import { DBDataTypeI } from '@consts/common/data';
import { ObjUnknownI } from '@interfaces/common/data';
import { CityFetchOneParamsI, CityFetchForClientParamsI, CityFetchsertParamsI } from '@schemas/master/city';
import { CountryModule } from './country';

export const CityModule = {
  fetchById: async (id: string, isThrowingError: boolean = true): Promise<CityNestedClientI> => {
    const cityRawDataResult = await prismaPG.cityRawData.findFirst({
      where: {
        id,
      },
      select: {
        id: true,
        name: true,
      },
    });
    if (cityRawDataResult) {
      return {
        ...cityRawDataResult,
        dataType: 'raw',
      } as CityNestedClientI;
    }
    const cityResult = await prismaPG.city.findFirst({
      where: { id },
      select: {
        id: true,
        name: true,
      },
    });

    if (cityResult) {
      return {
        ...(cityResult as ObjUnknownI),
        dataType: 'master',
      } as CityNestedClientI;
    }

    if (isThrowingError) {
      throw new AppError('CITY001');
    }
  },
  fetchByOne: async (filtersP: CityFetchOneParamsI, _isThrowingError: boolean = true): Promise<CityNestedClientI> => {
    if (filtersP.city.dataType === 'raw') {
      const filters: Prisma.CityRawDataWhereInput = {
        id: filtersP.city.id,
      };
      if (filtersP.countryIso2?.length) {
        filters.countryIso2 = filtersP.countryIso2;
      }
      const cityRawDataResult = await prismaPG.cityRawData.findFirst({
        where: filters,
        select: {
          id: true,
          name: true,
        },
      });
      if (cityRawDataResult) {
        return {
          ...cityRawDataResult,
          dataType: 'raw',
        } as CityNestedClientI;
      }
    } else if (filtersP.city.dataType === 'master') {
      const filters: Prisma.CityWhereInput = {
        id: filtersP.city.id,
      };
      if (filtersP.countryIso2?.length) {
        filters.countryIso2 = filtersP.countryIso2;
      }
      const cityResult = await prismaPG.city.findFirst({
        where: filters,
        select: {
          id: true,
          name: true,
        },
      });

      if (cityResult) {
        return {
          ...(cityResult as ObjUnknownI),
          dataType: 'master',
        } as CityNestedClientI;
      }
    }

    if (_isThrowingError) {
      throw new AppError('CITY001');
    }
  },
  fetchForClient: async (
    filtersP: CityFetchForClientParamsI,
    pagination: PaginationI = PAGINATION,
  ): Promise<CityNestedClientI[]> => {
    let citysResult: CityNestedClientI[] = [];
    const cityFilters: Prisma.CityWhereInput = {};
    const cityRawDataFilters: Prisma.CityRawDataWhereInput = {};
    if (filtersP.search?.length) {
      cityFilters.name = {
        startsWith: filtersP.search.trim().toLowerCase(),
        mode: 'insensitive',
      };
      cityRawDataFilters.name = {
        startsWith: filtersP.search.trim().toLowerCase(),
        mode: 'insensitive',
      };
    }
    if (filtersP.countryIso2?.length) {
      cityFilters.countryIso2 = filtersP.countryIso2;
      cityRawDataFilters.countryIso2 = filtersP.countryIso2;
    }
    const [cityResultTemp, cityRawDataTemp] = await Promise.all([
      prismaPG.city.findMany({
        where: cityFilters,
        skip: pagination.page,
        take: pagination.pageSize,
        orderBy: { name: 'asc' },
        select: { id: true, name: true },
      }),
      prismaPG.cityRawData.findMany({
        where: cityRawDataFilters,
        skip: pagination.page,
        take: pagination.pageSize,
        orderBy: { name: 'asc' },
        select: { id: true, name: true },
      }),
    ]);

    if (cityResultTemp?.length) {
      citysResult.push(
        ...cityResultTemp.map(
          (city) =>
            ({
              ...city,
              dataType: 'master',
            }) as CityNestedClientI,
        ),
      );
    }

    if (cityRawDataTemp?.length) {
      citysResult.push(
        ...cityResultTemp.map(
          (city) =>
            ({
              ...city,
              dataType: 'raw',
            }) as CityNestedClientI,
        ),
      );
    }
    if (citysResult?.length) {
      citysResult = sortArrayByString(citysResult, 'name', 'asc');
    }
    return citysResult;
  },
  fetchsert: async (params: CityFetchsertParamsI): Promise<CityNestedClientI> => {
    const [cityResult, cityRawDataResult] = await Promise.all([
      prismaPG.city.findFirst({
        where: {
          OR: [
            {
              name: {
                startsWith: params.name.trim().toLowerCase(),
                mode: 'insensitive',
              },
            },
          ],
        },
        select: { id: true, name: true },
      }),
      prismaPG.cityRawData.findFirst({
        where: {
          OR: [
            {
              name: {
                startsWith: params.name.trim().toLowerCase(),
                mode: 'insensitive',
              },
            },
          ],
        },
        select: { id: true, name: true },
      }),
    ]);
    if (cityResult) {
      return { ...cityResult, dataType: 'master' as DBDataTypeI } as CityNestedClientI;
    } else if (cityRawDataResult) {
      return { ...cityRawDataResult, dataType: 'raw' as DBDataTypeI } as CityNestedClientI;
    }
    const _countryResult = await CountryModule.fetchOne({ iso2: params.countryIso2 });
    const cityRawDataInput: Prisma.CityRawDataUncheckedCreateInput = {
      name: params.name,
      countryIso2: params.countryIso2,
    };
    if (params?.geoNameId?.length) {
      cityRawDataInput.geoNameId = params.geoNameId;
    }
    const result = await prismaPG.cityRawData.create({ data: cityRawDataInput });
    if (!result?.id) {
      throw new AppError('CITY002');
    }
    return { ...result, dataType: 'raw' as DBDataTypeI } as CityNestedClientI;
  },
};
