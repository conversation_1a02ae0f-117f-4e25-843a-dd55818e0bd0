import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type {
  ProfileSkillCreateManyWithEntityDegreeI,
  ProfileSkillCreateManyI,
  ProfileSkillCreateManyWithEntityDegreeResultI,
  ProfileSkillCreateManyWithEntityCertificateI,
  ProfileSkillCreateManyWithEntityCertificateResultI,
  ProfileSkillCreateManyWithExperienceShipI,
  ProfileSkillCreateManyWithExperienceShipResultI,
} from '@interfaces/career/skill';
import { PostgresTxnI } from '@interfaces/common/db';
import { FastifyRequestI, FastifyStateI } from '@interfaces/common/declaration';
import {
  DeleteManyForEntityCertificateCourseFitlerI,
  DeleteManyForEntityDegreeFitlerI,
  FetchForEntityCertificateCourseFilterI,
  FetchForEntityDegreeFilterI,
  FetchForExperienceShipFilterI,
  FetchSpecificForEntityCertificateCourseFilterI,
  FetchSpecificForEntityDegreeFilterI,
  SkillNestedClientI,
} from '@interfaces/company/skill';
import { Prisma } from '@prisma/postgres';
import type {
  CRUDExperienceShipSkillsI,
  ProfileSkillDeleteBodyI,
  ProfileSkillFetchForExternalClientI,
  ProfileSkillPostBodyI,
} from '@schemas/career/skill';
import type { IdTypeI } from '@schemas/common/common';
import { isFilled } from '@utils/data/object';

export const SkillModule = {
  deleteMany: async (request: FastifyRequestI, params: ProfileSkillDeleteBodyI): Promise<void> => {
    const filters: Prisma.ProfileSkillWhereInput = {
      profileId: request.profileId,
    };
    const { masterSkillIds, rawDataSkillIds } = params.skills.reduce(
      (acc, item) => {
        if (item.dataType === 'master') {
          acc.masterSkillIds.push(item.id);
        } else if (item.dataType === 'raw') {
          acc.rawDataSkillIds.push(item.id);
        }
        return acc;
      },
      { masterSkillIds: [], rawDataSkillIds: [] } as { masterSkillIds: string[]; rawDataSkillIds: string[] },
    );
    if (!masterSkillIds?.length && !rawDataSkillIds?.length) {
      throw new AppError('PFSKL005');
    }
    if (masterSkillIds?.length) {
      filters.skillId = {
        in: masterSkillIds,
      };
    }
    if (rawDataSkillIds?.length) {
      filters.skillRawDataId = {
        in: rawDataSkillIds,
      };
    }
    const existingProfileSkillsResult = await prismaPG.profileSkill.findMany({
      where: filters,
      select: {
        id: true,
      },
    });
    if (!existingProfileSkillsResult?.length) {
      throw new AppError('PFSKL001');
    }
    const profileSkillResult = await prismaPG.profileSkill.deleteMany({
      where: {
        id: {
          in: existingProfileSkillsResult.map(({ id }) => id),
        },
      },
    });
    if (!profileSkillResult.count) {
      throw new AppError('PFSKL009');
    }
    return;
  },
  createMany: async (params: ProfileSkillCreateManyI): Promise<number> => {
    const profileSkillFilter: Prisma.ProfileSkillWhereInput = {
      OR: [],
    };
    if (params?.masterSkills?.length) {
      profileSkillFilter.OR.push(
        ...params.masterSkills.map(
          (skillId) => ({ profileId: params.profileId, skillId }) as Prisma.ProfileSkillWhereInput,
        ),
      );
    }

    if (params?.rawDataSkills?.length) {
      profileSkillFilter.OR.push(
        ...params.rawDataSkills.map(
          (skillRawDataId) => ({ profileId: params.profileId, skillRawDataId }) as Prisma.ProfileSkillWhereInput,
        ),
      );
    }

    let profileSkillInputs: Prisma.ProfileSkillCreateManyInput[] = [];
    if (!params?.masterSkills?.length && !params?.rawDataSkills?.length) {
      throw new AppError('PFSKL005');
    }
    if (params?.masterSkills?.length) {
      profileSkillInputs.push(
        ...params.masterSkills.map(
          (skillItem) => ({ skillId: skillItem, profileId: params.profileId }) as Prisma.ProfileSkillCreateManyInput,
        ),
      );
    }
    if (params?.rawDataSkills?.length) {
      profileSkillInputs.push(
        ...params.rawDataSkills.map(
          (skillItem) =>
            ({ skillRawDataId: skillItem, profileId: params.profileId }) as Prisma.ProfileSkillCreateManyInput,
        ),
      );
    }
    const alreadyExistingProfileSkillsResult = await prismaPG.profileSkill.findMany({
      where: profileSkillFilter,
      select: { profileId: true, skillId: true, skillRawDataId: true },
    });
    if (alreadyExistingProfileSkillsResult?.length) {
      const alreadyExistingProfileSkillsSet: Set<string> = new Set<string>(
        alreadyExistingProfileSkillsResult?.map(
          (item) => `${item.profileId}-${item.skillId || null}-${item.skillRawDataId || null}`,
        ),
      );
      // If the ProfileSkill already exists then it will be removed from the profileSkillInputs
      profileSkillInputs = profileSkillInputs?.filter(
        (item) =>
          !alreadyExistingProfileSkillsSet.has(
            `${item.profileId}-${item.skillId || null}-${item.skillRawDataId || null}`,
          ),
      );
    }
    if (profileSkillInputs?.length) {
      const profileSkillsResult = await prismaPG.$transaction(async (txn) => {
        const profileSkillsResultTemp = await txn.profileSkill.createManyAndReturn({
          data: profileSkillInputs,
          select: {
            Skill: {
              select: {
                id: true,
                category: true,
              },
            },
            SkillRawData: {
              select: {
                id: true,
                category: true,
              },
            },
          },
          skipDuplicates: true,
        });
        let maritimeSkillsCount: number = 0;
        let otherSkillsCount: number = 0;
        profileSkillsResultTemp.forEach((item) => {
          if (item.Skill?.category) {
            if (item.Skill?.category === 'MARITIME') {
              ++maritimeSkillsCount;
            } else if (item.Skill.category === 'OTHER') {
              ++otherSkillsCount;
            }
          } else if (item.SkillRawData?.category) {
            if (item.SkillRawData.category === 'MARITIME') {
              ++maritimeSkillsCount;
            } else if (item.SkillRawData?.category === 'OTHER') {
              ++otherSkillsCount;
            }
          }
        });
        const updateProfileMetaParams: Prisma.ProfileMetaUncheckedUpdateInput = {};
        if (maritimeSkillsCount > 0) {
          updateProfileMetaParams.maritimeSkillsCount = {
            increment: maritimeSkillsCount,
          };
        }
        if (otherSkillsCount > 0) {
          updateProfileMetaParams.maritimeSkillsCount = {
            increment: otherSkillsCount,
          };
        }
        if (isFilled(updateProfileMetaParams)) {
          const _profileMetaResult = await txn.profileMeta.update({
            select: {
              profileId: true,
            },
            data: updateProfileMetaParams,
            where: {
              profileId: params.profileId,
            },
          });
        }
        return profileSkillsResultTemp;
      });

      return (alreadyExistingProfileSkillsResult?.length || 0) + (profileSkillsResult?.length || 0);
    }
    return alreadyExistingProfileSkillsResult?.length || 0;
  },
  createManyExternal: async (request: FastifyRequestI, params: ProfileSkillPostBodyI): Promise<number> => {
    const { masterSkills, rawDataSkills } = params.reduce(
      (acc, curr) => {
        if (curr.dataType === 'master') {
          acc.masterSkills.push(curr.id);
        } else if (curr.dataType === 'raw') {
          acc.rawDataSkills.push(curr.id);
        }
        return acc;
      },
      { masterSkills: [], rawDataSkills: [] } as { masterSkills: string[]; rawDataSkills: string[] },
    );
    if (!masterSkills?.length && !rawDataSkills?.length) {
      throw new AppError('PFSKL005');
    }
    return await SkillModule.createMany({ masterSkills, rawDataSkills, profileId: request.profileId });
  },
  createManyWithEntityDegree: async (
    params: ProfileSkillCreateManyWithEntityDegreeI,
  ): Promise<ProfileSkillCreateManyWithEntityDegreeResultI> => {
    const countProfileSkillResult = await SkillModule.createMany({
      profileId: params.profileId,
      masterSkills: params?.masterSkills,
      rawDataSkills: params?.rawDataSkills,
    });
    const profileSkillEntityDegreeFilter: Prisma.ProfileSkillEntityDegreeWhereInput = {
      OR: [],
    };
    if (params?.masterSkills?.length) {
      profileSkillEntityDegreeFilter.OR.push(
        ...params.masterSkills.map(
          (skillId) =>
            ({
              profileId: params.profileId,
              skillId,
              ...(params.entity.dataType === 'master'
                ? {
                    entityId: params.entity.id,
                  }
                : {
                    entityRawDataId: params.entity.id,
                  }),
              ...(params.degree.dataType === 'master'
                ? {
                    degreeId: params.degree.id,
                  }
                : {
                    degreeRawDataId: params.degree.id,
                  }),
            }) as Prisma.ProfileSkillEntityDegreeWhereInput,
        ),
      );
    }
    if (params?.rawDataSkills?.length) {
      profileSkillEntityDegreeFilter.OR.push(
        ...params.rawDataSkills.map(
          (skillRawDataId) =>
            ({
              profileId: params.profileId,
              skillRawDataId,
              ...(params.entity.dataType === 'master'
                ? {
                    entityId: params.entity.id,
                  }
                : {
                    entityRawDataId: params.entity.id,
                  }),
              ...(params.degree.dataType === 'master'
                ? {
                    degreeId: params.degree.id,
                  }
                : {
                    degreeRawDataId: params.degree.id,
                  }),
            }) as Prisma.ProfileSkillEntityDegreeWhereInput,
        ),
      );
    }
    const alreadyExistingProfileSkillEntityDegreeResult = await prismaPG.profileSkillEntityDegree.findMany({
      where: profileSkillEntityDegreeFilter,
      select: {
        profileId: true,
        skillId: true,
        skillRawDataId: true,
        entityId: true,
        entityRawDataId: true,
        degreeId: true,
        degreeRawDataId: true,
      },
    });
    let profileSkillEntityDegreeInputs: Prisma.ProfileSkillEntityDegreeCreateManyInput[] = [];

    if (!params?.masterSkills?.length && !params?.rawDataSkills?.length) {
      throw new AppError('PFSKL005');
    }
    if (params?.masterSkills?.length) {
      profileSkillEntityDegreeInputs.push(
        ...params.masterSkills.map(
          (skillId) =>
            ({
              skillId,
              profileId: params.profileId,

              ...(params.entity.dataType === 'master'
                ? {
                    entityId: params.entity.id,
                  }
                : {
                    entityRawDataId: params.entity.id,
                  }),

              ...(params.degree.dataType === 'master'
                ? {
                    degreeId: params.degree.id,
                  }
                : {
                    degreeRawDataId: params.degree.id,
                  }),
            }) as Prisma.ProfileSkillEntityDegreeCreateManyInput,
        ),
      );
    }
    if (params?.rawDataSkills?.length) {
      profileSkillEntityDegreeInputs.push(
        ...params.rawDataSkills.map(
          (skillRawDataId) =>
            ({
              skillRawDataId,
              profileId: params.profileId,
              ...(params.entity.dataType === 'master'
                ? {
                    entityId: params.entity.id,
                  }
                : {
                    entityRawDataId: params.entity.id,
                  }),
              ...(params.degree.dataType === 'master'
                ? {
                    degreeId: params.degree.id,
                  }
                : {
                    degreeRawDataId: params.degree.id,
                  }),
            }) as Prisma.ProfileSkillEntityDegreeCreateManyInput,
        ),
      );
    }

    if (alreadyExistingProfileSkillEntityDegreeResult?.length) {
      const alreadyExistingProfileSkillEntitySet: Set<string> = new Set<string>(
        alreadyExistingProfileSkillEntityDegreeResult?.map(
          (item) =>
            `${item.profileId}-${item.skillId || null}-${item.skillRawDataId || null}-${item.entityId || null}-${item.entityRawDataId || null}-${item.entityId || null}-${item.entityRawDataId || null}-${item.degreeId || null}-${item.degreeRawDataId || null}`,
        ),
      );
      // If the ProfileSkillEntity already exists then it will be removed from the profileSkillEntityDegreeInputs
      profileSkillEntityDegreeInputs = profileSkillEntityDegreeInputs?.filter(
        (item) =>
          !alreadyExistingProfileSkillEntitySet.has(
            `${item.profileId}-${item.skillId || null}-${item.skillRawDataId || null}-${item.entityId || null}-${item.entityRawDataId || null}-${item.degreeId || null}-${item.degreeRawDataId || null}`,
          ),
      );
    }

    if (profileSkillEntityDegreeInputs?.length) {
      const profileSkillEntityResult = await prismaPG.profileSkillEntityDegree.createMany({
        data: profileSkillEntityDegreeInputs,
        skipDuplicates: true,
      });
      return {
        countProfileSkill: countProfileSkillResult || 0,
        countProfileSkillEntityDegree:
          (alreadyExistingProfileSkillEntityDegreeResult?.length || 0) + (profileSkillEntityResult?.count || 0),
      };
    }
    return {
      countProfileSkill: countProfileSkillResult || 0,
      countProfileSkillEntityDegree: alreadyExistingProfileSkillEntityDegreeResult?.length || 0,
    };
  },
  createManyWithEntityCertificate: async (
    params: ProfileSkillCreateManyWithEntityCertificateI,
  ): Promise<ProfileSkillCreateManyWithEntityCertificateResultI> => {
    const countProfileSkillResult = await SkillModule.createMany({
      profileId: params.profileId,
      masterSkills: params?.masterSkills,
      rawDataSkills: params?.rawDataSkills,
    });
    const profileSkillEntityCertificateFilter: Prisma.ProfileSkillEntityCertificateWhereInput = {
      OR: [],
    };
    if (params?.masterSkills?.length) {
      profileSkillEntityCertificateFilter.OR.push(
        ...params.masterSkills.map(
          (skillId) =>
            ({
              profileId: params.profileId,
              skillId,
              ...(params.entity.dataType === 'master'
                ? {
                    entityId: params.entity.id,
                  }
                : {
                    entityRawDataId: params.entity.id,
                  }),
              ...(params.certificate.dataType === 'master'
                ? {
                    certificateId: params.certificate.id,
                  }
                : {
                    certificateRawDataId: params.certificate.id,
                  }),
            }) as Prisma.ProfileSkillEntityCertificateWhereInput,
        ),
      );
    }
    if (params?.rawDataSkills?.length) {
      profileSkillEntityCertificateFilter.OR.push(
        ...params.rawDataSkills.map(
          (skillRawDataId) =>
            ({
              profileId: params.profileId,
              skillRawDataId,
              ...(params.entity.dataType === 'master'
                ? {
                    entityId: params.entity.id,
                  }
                : {
                    entityRawDataId: params.entity.id,
                  }),
              ...(params.certificate.dataType === 'master'
                ? {
                    certificateId: params.certificate.id,
                  }
                : {
                    certificateRawDataId: params.certificate.id,
                  }),
            }) as Prisma.ProfileSkillEntityCertificateWhereInput,
        ),
      );
    }
    const alreadyExistingProfileSkillEntityCertificateResult = await prismaPG.profileSkillEntityCertificate.findMany({
      where: profileSkillEntityCertificateFilter,
      select: {
        profileId: true,
        skillId: true,
        skillRawDataId: true,
        entityId: true,
        entityRawDataId: true,
        certificateId: true,
        certificateRawDataId: true,
      },
    });
    let profileSkillEntityCertificateInputs: Prisma.ProfileSkillEntityCertificateCreateManyInput[] = [];

    if (!params?.masterSkills?.length && !params?.rawDataSkills?.length) {
      throw new AppError('PFSKL005');
    }
    if (params?.masterSkills?.length) {
      profileSkillEntityCertificateInputs.push(
        ...params.masterSkills.map(
          (skillId) =>
            ({
              skillId,
              profileId: params.profileId,

              ...(params.entity.dataType === 'master'
                ? {
                    entityId: params.entity.id,
                  }
                : {
                    entityRawDataId: params.entity.id,
                  }),

              ...(params.certificate.dataType === 'master'
                ? {
                    certificateId: params.certificate.id,
                  }
                : {
                    certificateRawDataId: params.certificate.id,
                  }),
            }) as Prisma.ProfileSkillEntityCertificateCreateManyInput,
        ),
      );
    }
    if (params?.rawDataSkills?.length) {
      profileSkillEntityCertificateInputs.push(
        ...params.rawDataSkills.map(
          (skillRawDataId) =>
            ({
              skillRawDataId,
              profileId: params.profileId,
              ...(params.entity.dataType === 'master'
                ? {
                    entityId: params.entity.id,
                  }
                : {
                    entityRawDataId: params.entity.id,
                  }),
              ...(params.certificate.dataType === 'master'
                ? {
                    certificateId: params.certificate.id,
                  }
                : {
                    certificateRawDataId: params.certificate.id,
                  }),
            }) as Prisma.ProfileSkillEntityCertificateCreateManyInput,
        ),
      );
    }

    if (alreadyExistingProfileSkillEntityCertificateResult?.length) {
      const alreadyExistingProfileSkillEntitySet: Set<string> = new Set<string>(
        alreadyExistingProfileSkillEntityCertificateResult?.map(
          (item) =>
            `${item.profileId}-${item.skillId || null}-${item.skillRawDataId || null}-${item.entityId || null}-${item.entityRawDataId || null}-${item.entityId || null}-${item.entityRawDataId || null}-${item.certificateId || null}-${item.certificateRawDataId || null}`,
        ),
      );
      // If the ProfileSkillEntity already exists then it will be removed from the profileSkillEntityCertificateInputs
      profileSkillEntityCertificateInputs = profileSkillEntityCertificateInputs?.filter(
        (item) =>
          !alreadyExistingProfileSkillEntitySet.has(
            `${item.profileId}-${item.skillId || null}-${item.skillRawDataId || null}-${item.entityId || null}-${item.entityRawDataId || null}-${item.certificateId || null}-${item.certificateRawDataId || null}`,
          ),
      );
    }

    if (profileSkillEntityCertificateInputs?.length) {
      const profileSkillEntityResult = await prismaPG.profileSkillEntityCertificate.createMany({
        data: profileSkillEntityCertificateInputs,
        skipDuplicates: true,
      });
      return {
        countProfileSkill: countProfileSkillResult || 0,
        countProfileSkillEntityCertificate:
          (alreadyExistingProfileSkillEntityCertificateResult?.length || 0) + (profileSkillEntityResult?.count || 0),
      };
    }
    return {
      countProfileSkill: countProfileSkillResult || 0,
      countProfileSkillEntityCertificate: alreadyExistingProfileSkillEntityCertificateResult?.length || 0,
    };
  },
  createManyWithExperienceShip: async ({
    experienceShipId,
    masterSkills,
    profileId,
    rawDataSkills,
  }: ProfileSkillCreateManyWithExperienceShipI): Promise<ProfileSkillCreateManyWithExperienceShipResultI> => {
    const countProfileSkillResult = await SkillModule.createMany({
      profileId: profileId,
      masterSkills: masterSkills,
      rawDataSkills: rawDataSkills,
    });
    const profileSkillExperienceShipFilter: Prisma.ProfileSkillExperienceShipWhereInput = {
      OR: [],
    };
    if (masterSkills?.length) {
      profileSkillExperienceShipFilter.OR.push(
        ...masterSkills.map(
          (skillId) =>
            ({
              profileId: profileId,
              skillId,
              experienceShipId,
            }) as Prisma.ProfileSkillExperienceShipWhereInput,
        ),
      );
    }
    if (rawDataSkills?.length) {
      profileSkillExperienceShipFilter.OR.push(
        ...rawDataSkills.map(
          (skillRawDataId) =>
            ({
              profileId: profileId,
              skillRawDataId,
              experienceShipId,
            }) as Prisma.ProfileSkillExperienceShipWhereInput,
        ),
      );
    }
    const alreadyExistingProfileSkillExperienceShipResult = await prismaPG.profileSkillExperienceShip.findMany({
      where: profileSkillExperienceShipFilter,
      select: {
        profileId: true,
        skillId: true,
        skillRawDataId: true,
        experienceShipId: true,
      },
    });
    let profileSkillExperienceShipInputs: Prisma.ProfileSkillExperienceShipUncheckedCreateInput[] = [];

    if (!masterSkills?.length && !rawDataSkills?.length) {
      throw new AppError('PFSKL005');
    }
    if (masterSkills?.length) {
      profileSkillExperienceShipInputs.push(
        ...masterSkills.map(
          (skillId) =>
            ({
              skillId,
              profileId,
              experienceShipId,
            }) as Prisma.ProfileSkillExperienceShipUncheckedCreateInput,
        ),
      );
    }
    if (rawDataSkills?.length) {
      profileSkillExperienceShipInputs.push(
        ...rawDataSkills.map(
          (skillRawDataId) =>
            ({
              skillRawDataId,
              profileId: profileId,
              experienceShipId,
            }) as Prisma.ProfileSkillExperienceShipUncheckedCreateInput,
        ),
      );
    }

    if (alreadyExistingProfileSkillExperienceShipResult?.length) {
      const alreadyExistingProfileSkillExperienceShipSet: Set<string> = new Set<string>(
        alreadyExistingProfileSkillExperienceShipResult?.map(
          (item) => `${item.profileId}-${item.skillId || null}-${item.skillRawDataId || null}-${item.experienceShipId}`,
        ),
      );
      // If the ProfileSkillEntity already exists then it will be removed from the profileSkillEntityDegreeInputs
      profileSkillExperienceShipInputs = profileSkillExperienceShipInputs?.filter(
        (item) =>
          !alreadyExistingProfileSkillExperienceShipSet.has(
            `${item.profileId}-${item?.skillId || null}-${item?.skillRawDataId || null}-${item?.experienceShipId}`,
          ),
      );
    }

    if (profileSkillExperienceShipInputs?.length) {
      const profileSkillEntityResult = await prismaPG.profileSkillExperienceShip.createMany({
        data: profileSkillExperienceShipInputs,
        skipDuplicates: true,
      });
      return {
        countProfileSkill: countProfileSkillResult || 0,
        countProfileSkillExperienceShip:
          (alreadyExistingProfileSkillExperienceShipResult?.length || 0) + (profileSkillEntityResult?.count || 0),
      };
    }
    return {
      countProfileSkill: countProfileSkillResult || 0,
      countProfileSkillExperienceShip: alreadyExistingProfileSkillExperienceShipResult?.length || 0,
    };
  },
  fetchForEntityDegree: async (
    fetchForEntityDegreeFilter: FetchForEntityDegreeFilterI,
  ): Promise<SkillNestedClientI[]> => {
    const filters: Prisma.ProfileSkillEntityDegreeWhereInput = {};
    if (fetchForEntityDegreeFilter?.entity?.dataType === 'master') {
      filters.entityId = fetchForEntityDegreeFilter.entity.id;
    } else if (fetchForEntityDegreeFilter?.entity?.dataType === 'raw') {
      filters.entityRawDataId = fetchForEntityDegreeFilter.entity.id;
    }
    if (fetchForEntityDegreeFilter?.degree?.dataType === 'master') {
      filters.degreeId = fetchForEntityDegreeFilter.degree.id;
    } else if (fetchForEntityDegreeFilter?.degree?.dataType === 'raw') {
      filters.degreeRawDataId = fetchForEntityDegreeFilter.degree.id;
    }
    const skillNestedClientResult: SkillNestedClientI[] = [];
    const skillsResult = await prismaPG.profileSkillEntityDegree.findMany({
      select: {
        id: true,
        Skill: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
        SkillRawData: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
      },
      where: filters,
    });
    if (skillsResult?.length) {
      skillNestedClientResult.push(
        ...skillsResult.map(
          (skill) =>
            (isFilled(skill.Skill)
              ? {
                  id: skill.Skill.id,
                  name: skill.Skill.name,
                  category: skill.Skill.category,
                  dataType: 'master',
                }
              : {
                  id: skill.SkillRawData.id,
                  name: skill.SkillRawData.name,
                  category: skill.SkillRawData.category,
                  dataType: 'raw',
                }) as SkillNestedClientI,
        ),
      );
    }
    return skillNestedClientResult;
  },
  fetchForEntityCertificateCourse: async (
    fetchForEntityCertificateCourseFilter: FetchForEntityCertificateCourseFilterI,
  ): Promise<SkillNestedClientI[]> => {
    const filters: Prisma.ProfileSkillEntityCertificateWhereInput = {};
    if (fetchForEntityCertificateCourseFilter?.entity?.dataType === 'master') {
      filters.entityId = fetchForEntityCertificateCourseFilter.entity.id;
    } else if (fetchForEntityCertificateCourseFilter?.entity?.dataType === 'raw') {
      filters.entityRawDataId = fetchForEntityCertificateCourseFilter.entity.id;
    }
    if (fetchForEntityCertificateCourseFilter?.certificateCourse?.dataType === 'master') {
      filters.certificateId = fetchForEntityCertificateCourseFilter.certificateCourse.id;
    } else if (fetchForEntityCertificateCourseFilter?.certificateCourse?.dataType === 'raw') {
      filters.certificateRawDataId = fetchForEntityCertificateCourseFilter.certificateCourse.id;
    }
    const skillNestedClientResult: SkillNestedClientI[] = [];
    const skillsResult = await prismaPG.profileSkillEntityCertificate.findMany({
      select: {
        id: true,
        Skill: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
        SkillRawData: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
      },
      where: filters,
    });
    if (skillsResult?.length) {
      skillNestedClientResult.push(
        ...skillsResult.map(
          (skill) =>
            (isFilled(skill.Skill)
              ? {
                  id: skill.Skill.id,
                  name: skill.Skill.name,
                  category: skill.Skill.category,
                  dataType: 'master',
                }
              : {
                  id: skill.Skill.id,
                  name: skill.Skill.name,
                  category: skill.Skill.category,
                  dataType: 'raw',
                }) as SkillNestedClientI,
        ),
      );
    }
    return skillNestedClientResult;
  },
  fetchSpecificForEntityDegree: async (
    fetchForEntityDegreeFilter: FetchSpecificForEntityDegreeFilterI,
  ): Promise<IdTypeI[]> => {
    if (!fetchForEntityDegreeFilter?.idTypes?.length) {
      throw new AppError('PFSKL005');
    }
    const filters: Prisma.ProfileSkillEntityDegreeWhereInput = {
      profileId: fetchForEntityDegreeFilter.profileId,
    };
    if (fetchForEntityDegreeFilter.idTypes?.length) {
      const skillIds: string[] = [];
      const skillRawDataIds: string[] = [];

      fetchForEntityDegreeFilter.idTypes.forEach((idTypeItem) => {
        if (idTypeItem.dataType === 'master') {
          skillIds.push(idTypeItem.id);
        } else if (idTypeItem.dataType === 'raw') {
          skillRawDataIds.push(idTypeItem.id);
        }
      });
      if (skillIds?.length) {
        filters.skillId = {
          in: skillIds,
        };
      }
      if (skillRawDataIds?.length) {
        filters.skillRawDataId = {
          in: skillRawDataIds,
        };
      }
    }
    if (fetchForEntityDegreeFilter?.entity?.dataType === 'master') {
      filters.entityId = fetchForEntityDegreeFilter.entity.id;
    } else if (fetchForEntityDegreeFilter?.entity?.dataType === 'raw') {
      filters.entityRawDataId = fetchForEntityDegreeFilter.entity.id;
    }
    if (fetchForEntityDegreeFilter?.degree?.dataType === 'master') {
      filters.degreeId = fetchForEntityDegreeFilter.degree.id;
    } else if (fetchForEntityDegreeFilter?.degree?.dataType === 'raw') {
      filters.degreeRawDataId = fetchForEntityDegreeFilter.degree.id;
    }
    const idTypesResult: IdTypeI[] = [];
    const skillEntityDegreeResult = await prismaPG.profileSkillEntityDegree.findMany({
      select: {
        id: true,
        Skill: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
        SkillRawData: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
      },
      where: filters,
    });
    if (skillEntityDegreeResult?.length) {
      idTypesResult.push(
        ...skillEntityDegreeResult.map(
          (skill) =>
            (isFilled(skill.Skill)
              ? {
                  id: skill.Skill.id,
                  dataType: 'master',
                }
              : {
                  id: skill.Skill.id,
                  dataType: 'raw',
                }) as IdTypeI,
        ),
      );
    }
    return idTypesResult;
  },
  deleteManyForEntityDegree: async (
    deleteManyForEntityDegreeFitler: DeleteManyForEntityDegreeFitlerI,
  ): Promise<number> => {
    const skillIds: string[] = [];
    const skillRawDataIds: string[] = [];
    deleteManyForEntityDegreeFitler.idTypes.forEach((skillIdTypeItem) => {
      if (skillIdTypeItem.dataType === 'master') {
        skillIds.push(skillIdTypeItem.id);
      } else if (skillIdTypeItem.dataType === 'raw') {
        skillRawDataIds.push(skillIdTypeItem.id);
      }
    });
    const filter: Prisma.ProfileSkillEntityDegreeWhereInput = {
      profileId: deleteManyForEntityDegreeFitler.profileId,
    };
    if (deleteManyForEntityDegreeFitler.entity?.dataType === 'master') {
      filter.entityId = deleteManyForEntityDegreeFitler.entity.id;
    } else {
      filter.entityRawDataId = deleteManyForEntityDegreeFitler.entity.id;
    }

    if (deleteManyForEntityDegreeFitler.degree?.dataType === 'master') {
      filter.degreeId = deleteManyForEntityDegreeFitler.degree.id;
    } else {
      filter.degreeRawDataId = deleteManyForEntityDegreeFitler.degree.id;
    }
    if (skillIds?.length) {
      filter.skillId = {
        in: skillIds,
      };
    }
    if (skillRawDataIds?.length) {
      filter.skillRawDataId = {
        in: skillRawDataIds,
      };
    }
    const deletedCountResult = await prismaPG.profileSkillEntityDegree.deleteMany({
      where: filter,
    });
    return deletedCountResult?.count ?? 0;
  },
  fetchSpecificForEntityCertificate: async (
    fetchForEntityCertificateFilter: FetchSpecificForEntityCertificateCourseFilterI,
  ): Promise<IdTypeI[]> => {
    if (!fetchForEntityCertificateFilter?.idTypes?.length) {
      throw new AppError('PFSKL005');
    }
    const filters: Prisma.ProfileSkillEntityCertificateWhereInput = {
      profileId: fetchForEntityCertificateFilter.profileId,
    };
    if (fetchForEntityCertificateFilter.idTypes?.length) {
      const skillIds: string[] = [];
      const skillRawDataIds: string[] = [];

      fetchForEntityCertificateFilter.idTypes.forEach((idTypeItem) => {
        if (idTypeItem.dataType === 'master') {
          skillIds.push(idTypeItem.id);
        } else if (idTypeItem.dataType === 'raw') {
          skillRawDataIds.push(idTypeItem.id);
        }
      });
      if (skillIds?.length) {
        filters.skillId = {
          in: skillIds,
        };
      }
      if (skillRawDataIds?.length) {
        filters.skillRawDataId = {
          in: skillRawDataIds,
        };
      }
    }
    if (fetchForEntityCertificateFilter?.entity?.dataType === 'master') {
      filters.entityId = fetchForEntityCertificateFilter.entity.id;
    } else if (fetchForEntityCertificateFilter?.entity?.dataType === 'raw') {
      filters.entityRawDataId = fetchForEntityCertificateFilter.entity.id;
    }
    if (fetchForEntityCertificateFilter?.certificateCourse?.dataType === 'master') {
      filters.certificateId = fetchForEntityCertificateFilter.certificateCourse.id;
    } else if (fetchForEntityCertificateFilter?.certificateCourse?.dataType === 'raw') {
      filters.certificateRawDataId = fetchForEntityCertificateFilter.certificateCourse.id;
    }
    const idTypesResult: IdTypeI[] = [];
    const skillEntityCertificateResult = await prismaPG.profileSkillEntityCertificate.findMany({
      select: {
        id: true,
        Skill: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
        SkillRawData: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
      },
      where: filters,
    });
    if (skillEntityCertificateResult?.length) {
      idTypesResult.push(
        ...skillEntityCertificateResult.map(
          (skill) =>
            (isFilled(skill.Skill)
              ? {
                  id: skill.Skill.id,
                  dataType: 'master',
                }
              : {
                  id: skill.Skill.id,
                  dataType: 'raw',
                }) as IdTypeI,
        ),
      );
    }
    return idTypesResult;
  },
  deleteManyForEntityCertificate: async (
    deleteManyForEntityCertificateFitler: DeleteManyForEntityCertificateCourseFitlerI,
  ): Promise<number> => {
    const skillIds: string[] = [];
    const skillRawDataIds: string[] = [];
    deleteManyForEntityCertificateFitler.idTypes.forEach((skillIdTypeItem) => {
      if (skillIdTypeItem.dataType === 'master') {
        skillIds.push(skillIdTypeItem.id);
      } else if (skillIdTypeItem.dataType === 'raw') {
        skillRawDataIds.push(skillIdTypeItem.id);
      }
    });
    const filter: Prisma.ProfileSkillEntityCertificateWhereInput = {
      profileId: deleteManyForEntityCertificateFitler.profileId,
    };
    if (deleteManyForEntityCertificateFitler.entity?.dataType === 'master') {
      filter.entityId = deleteManyForEntityCertificateFitler.entity.id;
    } else {
      filter.entityRawDataId = deleteManyForEntityCertificateFitler.entity.id;
    }

    if (deleteManyForEntityCertificateFitler.certificateCourse?.dataType === 'master') {
      filter.certificateId = deleteManyForEntityCertificateFitler.certificateCourse.id;
    } else {
      filter.certificateRawDataId = deleteManyForEntityCertificateFitler.certificateCourse.id;
    }
    if (skillIds?.length) {
      filter.skillId = {
        in: skillIds,
      };
    }
    if (skillRawDataIds?.length) {
      filter.skillRawDataId = {
        in: skillRawDataIds,
      };
    }
    const deletedCountResult = await prismaPG.profileSkillEntityCertificate.deleteMany({
      where: filter,
    });
    return deletedCountResult?.count ?? 0;
  },
  fetchForExperienceShip: async ({
    experienceShipId,
    profileId,
  }: FetchForExperienceShipFilterI): Promise<SkillNestedClientI[]> => {
    const filters: Prisma.ProfileSkillExperienceShipWhereInput = { experienceShipId, profileId };

    const skillNestedClientResult: SkillNestedClientI[] = [];
    const skillsResult = await prismaPG.profileSkillExperienceShip.findMany({
      select: {
        id: true,
        Skill: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
        SkillRawData: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
      },
      where: filters,
    });
    if (skillsResult?.length) {
      skillNestedClientResult.push(
        ...skillsResult.map(
          (skill) =>
            (isFilled(skill.Skill)
              ? {
                  id: skill.Skill.id,
                  name: skill.Skill.name,
                  category: skill.Skill.category,
                  dataType: 'master',
                }
              : {
                  id: skill.Skill.id,
                  name: skill.Skill.name,
                  category: skill.Skill.category,
                  dataType: 'raw',
                }) as SkillNestedClientI,
        ),
      );
    }
    return skillNestedClientResult;
  },
  fetchForExternalClient: async (params: ProfileSkillFetchForExternalClientI): Promise<SkillNestedClientI[]> => {
    const profileSkillResult = await prismaPG.profileSkill.findMany({
      select: {
        Skill: {
          select: {
            id: true,
            name: true,
          },
        },
        SkillRawData: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      where: {
        profileId: params.profileId,
        OR: [
          {
            Skill: {
              category: params.category,
            },
          },
          {
            SkillRawData: {
              category: params.category,
            },
          },
        ],
      },
      skip: params.page,
      take: params.pageSize,
    });
    const skillNestedClient: SkillNestedClientI[] = [];
    if (profileSkillResult?.length) {
      skillNestedClient.push(
        ...profileSkillResult.map(
          (skillItem) =>
            (isFilled(skillItem.Skill)
              ? {
                  id: skillItem.Skill.id,
                  name: skillItem.Skill.name,
                  category: params.category,
                  dataType: 'master',
                }
              : {
                  id: skillItem.SkillRawData.id,
                  name: skillItem.SkillRawData.name,
                  category: params.category,
                  dataType: 'raw',
                }) as SkillNestedClientI,
        ),
      );
    }
    return skillNestedClient;
  },
  crudExperienceShipSkills: async (
    state: FastifyStateI,
    txn: PostgresTxnI,
    { experienceShipId, skills }: CRUDExperienceShipSkillsI,
  ): Promise<void> => {
    const profileId = state.profileId;

    const toCreateProfileSkills: Prisma.ProfileSkillUncheckedCreateInput[] = [];

    const toCreateShipSkills: Prisma.ProfileSkillExperienceShipUncheckedCreateInput[] = [];

    const toDeleteShipSkillIds: Set<string> = new Set<string>();

    const toDeleteShipSkillRawDataIds: Set<string> = new Set<string>();

    skills.forEach((element) => {
      switch (element.opr) {
        case 'CREATE': {
          switch (element.dataType) {
            case 'master': {
              toCreateProfileSkills.push({
                profileId,
                skillId: element.id,
              });
              toCreateShipSkills.push({
                experienceShipId,
                profileId,
                skillId: element.id,
              });
              break;
            }
            case 'raw': {
              toCreateProfileSkills.push({
                profileId,
                skillRawDataId: element.id,
              });
              toCreateShipSkills.push({
                experienceShipId,
                profileId,
                skillId: element.id,
              });
              break;
            }
          }
          break;
        }
        case 'DELETE': {
          switch (element.dataType) {
            case 'master': {
              toDeleteShipSkillIds.add(element.id);
              break;
            }
            case 'raw': {
              toDeleteShipSkillRawDataIds.add(element.id);
              break;
            }
          }
          break;
        }
      }
    });

    const toDeleteShipSkillsFilter: Prisma.ProfileSkillExperienceShipWhereInput = {
      skillId: {
        in: [],
      },
      skillRawDataId: {
        in: [],
      },
    };
    if (toDeleteShipSkillIds?.size) {
      toDeleteShipSkillsFilter.skillId = {
        in: Array.from(toDeleteShipSkillIds),
      };
    }
    if (toDeleteShipSkillRawDataIds?.size) {
      toDeleteShipSkillsFilter.skillRawDataId = {
        in: Array.from(toDeleteShipSkillRawDataIds),
      };
    }
    const countCurrent = await txn.profileSkillExperienceShip.count({ where: { profileId, experienceShipId } });

    const countToAdd = toCreateShipSkills?.length;
    const countToDelete = toDeleteShipSkillIds.size + toDeleteShipSkillRawDataIds.size;
    if (countCurrent + countToAdd - countToDelete > 8) {
      throw new AppError('EXP015');
    }
    await Promise.all([
      toCreateProfileSkills?.length
        ? txn.profileSkill.createMany({
            data: toCreateProfileSkills,
          })
        : null,
      toCreateShipSkills?.length
        ? txn.profileSkillExperienceShip.createMany({
            data: toCreateShipSkills,
          })
        : null,
      toDeleteShipSkillIds?.size || toDeleteShipSkillRawDataIds?.size
        ? txn.profileSkillExperienceShip.deleteMany({
            where: toDeleteShipSkillsFilter,
          })
        : null,
    ]);
  },
};
