import { prismaPG } from '@config/db';
import { PortVisitorFetchRawResultI, PortVisitorFetchResultI } from '@interfaces/career/portVisit';
import { TotalDataI } from '@interfaces/common/data';
import { PortVisitFetchI } from '@schemas/port/portVisit';

export const PortVisitModule = {
  fetch: async ({ profileId, page, pageSize }: PortVisitFetchI): Promise<TotalDataI<PortVisitorFetchResultI>> => {
    const [portsRawResult, totalResult] = await Promise.all([
      prismaPG.$queryRaw<PortVisitorFetchRawResultI[]>`
      SELECT
        COALESCE(
          p."unLocode",
          prw."unLocode"
        ) AS "unLocode",
        COALESCE(
          p."name",
          prw."name",
          'Unknown Port'
        ) AS "name",
        COALESCE(
          c1."iso2",
          c2."iso2"
        ) AS "countryIso2",
        COALESCE(
          c1."name",
          c2."name"
        ) AS "countryName"
      FROM
        "port"."PortVisitor" v
      LEFT JOIN "port"."Port" p
        ON p."unLocode" = v."portUnLocode"
      LEFT JOIN "master"."Country" c1
        ON c1."iso2" = p."countryIso2"
      LEFT JOIN "rawData"."PortRawData" prw
        ON prw."unLocode" = v."portRawDataUnLocode"
      LEFT JOIN "master"."Country" c2
        ON c2."iso2" = prw."countryIso2"
      WHERE v."profileId" = ${profileId}::uuid
      ORDER BY v."createdAt" DESC
      OFFSET ${page * pageSize}
      LIMIT ${pageSize}
    `,
      prismaPG.portVisitor.count({
        where: {
          profileId,
        },
      }),
    ]);
    const portsResult: PortVisitorFetchResultI[] = [];
    if (portsRawResult?.length) {
      portsResult.push(
        ...portsRawResult.map(
          (item) =>
            ({
              unLocode: item.unLocode,
              name: item.name,
              country: {
                iso2: item.countryIso2,
                name: item.countryName,
              },
            }) as PortVisitorFetchResultI,
        ),
      );
    }
    return {
      data: portsResult,
      total: totalResult,
    };
  },
};
