import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { PAGINATION } from '@consts/common/pagination';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { ProfileItemI, ScrapBookReactionFetchForClientI } from '@interfaces/port/reaction';
import { Prisma } from '@prisma/postgres';
import type { PaginationI } from '@schemas/common/common';
import type {
  ScrapBookReactionpUsertOneParamsI,
  ScrapBookReactionPostIdParamsI,
  ScrapBookReactionFetchForClientParamsI,
} from '@schemas/port/reaction';

export const ScrapBookReactionModule = {
  fetchForClient: async (
    params: ScrapBookReactionFetchForClientParamsI,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ reactions: ScrapBookReactionFetchForClientI[]; totalCount: number }> => {
    if (!params.scrapBookPostId) {
      throw new AppError('SCBKRCN006');
    }

    const filters: Prisma.ScrapBookReactionWhereInput = {
      scrapBookPostId: params.scrapBookPostId,
    };

    const total = await prismaPG.scrapBookReaction.count({
      where: filters,
    });

    const skip = pagination.page > 0 ? (pagination.page - 1) * pagination.pageSize : 0;

    const scrapBookReactionResult = await prismaPG.scrapBookReaction.findMany({
      where: filters,
      select: {
        reactionType: true,
        Profile: {
          select: {
            id: true,
            name: true,
            avatar: true,
            designationText: true,
            designationAlternativeId: true,
            designationRawDataId: true,
            entityText: true,
            entityId: true,
            entityRawDataId: true,
          },
        },
      },
      skip: skip,
      take: pagination.pageSize,
      orderBy: {
        createdAt: 'desc',
      },
    });

    const transformProfile = (profile: ProfileItemI) => ({
      id: profile.id,
      name: profile.name,
      avatar: profile.avatar,
      designation: profile.designationAlternativeId
        ? {
            id: profile.designationAlternativeId,
            name: profile.designationText || '',
            dataType: 'master' as const,
          }
        : profile.designationRawDataId
          ? {
              id: profile.designationRawDataId,
              name: profile.designationText || '',
              dataType: 'raw' as const,
            }
          : null,
      entity: profile.entityId
        ? {
            id: profile.entityId,
            name: profile.entityText || '',
            dataType: 'master' as const,
          }
        : profile.entityRawDataId
          ? {
              id: profile.entityRawDataId,
              name: profile.entityText || '',
              dataType: 'raw' as const,
            }
          : null,
    });

    const scrapBookReactionFetchForClientResult: ScrapBookReactionFetchForClientI[] = scrapBookReactionResult.map(
      (reaction) => ({
        reactionType: reaction.reactionType,
        Profile: transformProfile(reaction.Profile),
      }),
    );

    return { reactions: scrapBookReactionFetchForClientResult, totalCount: total };
  },
  upsertOne: async (state: FastifyStateI, params: ScrapBookReactionpUsertOneParamsI): Promise<void> => {
    if (!state.profileId) {
      throw new AppError('SCBKRCN005');
    }

    if (!params.scrapBookPostId) {
      throw new AppError('SCBKRCN006');
    }

    if (!params.reactionType) {
      throw new AppError('SCBKRCN007');
    }

    const postExists = await prismaPG.scrapBookPost.findUnique({
      where: { id: params.scrapBookPostId },
      select: { id: true },
    });

    if (!postExists) {
      throw new AppError('SCBKRCN008');
    }

    const whereCondition = {
      profileId_scrapBookPostId: {
        profileId: state.profileId,
        scrapBookPostId: params.scrapBookPostId,
      },
    };

    try {
      await prismaPG.$transaction(async (tx) => {
        const existingReaction = await tx.scrapBookReaction.findUnique({
          where: whereCondition,
          select: { reactionType: true },
        });

        const scrapBookReactionResult = await tx.scrapBookReaction.upsert({
          create: {
            profileId: state.profileId,
            scrapBookPostId: params.scrapBookPostId,
            reactionType: params.reactionType,
          },
          update: {
            reactionType: params.reactionType,
          },
          where: whereCondition,
          select: {
            reactionType: true,
          },
        });

        if (!scrapBookReactionResult) {
          throw new AppError('SCBKRCN010');
        }

        if (!existingReaction) {
          await tx.scrapBookPost.update({
            where: { id: params.scrapBookPostId },
            data: {
              reactionCount: {
                increment: 1,
              },
            },
          });
        }
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          throw new AppError('SCBKRCN008');
        }
        if (error.code === 'P2025') {
          throw new AppError('SCBKRCN001');
        }
      }
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('SCBKRCN010');
    }
  },
  deleteOne: async (state: FastifyStateI, filtersP: ScrapBookReactionPostIdParamsI): Promise<void> => {
    if (!state.profileId) {
      throw new AppError('SCBKRCN005');
    }

    if (!filtersP.scrapBookPostId) {
      throw new AppError('SCBKRCN006');
    }

    const whereCondition = {
      profileId_scrapBookPostId: {
        profileId: state.profileId,
        scrapBookPostId: filtersP.scrapBookPostId,
      },
    };

    const existingReactionResult = await prismaPG.scrapBookReaction.findUnique({
      where: whereCondition,
      select: {
        reactionType: true,
      },
    });

    if (!existingReactionResult) {
      throw new AppError('SCBKRCN001');
    }

    try {
      await prismaPG.$transaction(async (tx) => {
        const deletedReactionResult = await tx.scrapBookReaction.delete({
          where: whereCondition,
          select: { reactionType: true },
        });

        if (!deletedReactionResult) {
          throw new AppError('SCBKRCN009');
        }

        await tx.scrapBookPost.update({
          where: { id: filtersP.scrapBookPostId },
          data: {
            reactionCount: {
              decrement: 1,
            },
          },
        });
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new AppError('SCBKRCN001');
        }
        if (error.code === 'P2003') {
          throw new AppError('SCBKRCN008');
        }
      }
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('SCBKRCN009');
    }
  },
};
