import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { TotalDataI } from '@interfaces/common/data';
import { TotalI } from '@interfaces/common/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { DesignationNestedClientI } from '@interfaces/company/designation';
import type { EntityNestedExternalI } from '@interfaces/company/entity';
import type { PeopleI, PeopleRawI } from '@interfaces/user/profile';

import { Prisma } from '@prisma/postgres';
import type {
  PortVisitorFetchForClientParamsI,
  PortVisitorCreateOneParamsI,
  PortVisitorDeleteOneParamsI,
} from '@schemas/port/visitor';

export const PortVisitorModule = {
  fetchForClient: async (
    state: FastifyStateI,
    { unLocode, dataType, page, pageSize }: PortVisitorFetchForClientParamsI,
  ): Promise<TotalDataI<PeopleI>> => {
    const selfProfileId = state.profileId;
    const [visitorCountResult, visitorResultTemp] = await Promise.all([
      prismaPG.$queryRaw<TotalI[]>`
      SELECT
      COUNT(DISTINCT v."profileId") AS "total"
      FROM "port"."PortVisitor" v
      INNER JOIN "user"."Profile" u ON u."id" = v."profileId"
      WHERE
        ${
          dataType === 'raw'
            ? Prisma.sql`v."portRawDataUnLocode" = ${unLocode}`
            : Prisma.sql`v."portUnLocode" = ${unLocode}`
        }
        AND NOT EXISTS (
          SELECT 1
          FROM "network"."BlockedProfile" b
          WHERE (
            (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = u."id")
            OR
            (b."blockerId" = u."id" AND b."blockedId" = ${selfProfileId}::uuid)
          )
          LIMIT 1
        )
    `,
      prismaPG.$queryRaw<PeopleRawI[]>`
        SELECT
          DISTINCT ON (u."id")
          u."id",
          u."name",
          u."avatar",
          u."designationText",
          u."designationAlternativeId",
          u."designationRawDataId",
          u."entityText",
          u."entityId",
          u."entityRawDataId",
          CASE
            WHEN u."id" = ${selfProfileId}::uuid THEN false
            ELSE EXISTS (
              SELECT 1
              FROM "network"."Connection" c
              WHERE c."profileId" = ${selfProfileId}::uuid
                AND c."connectedId" = u."id"
              LIMIT 1
            )
          END AS "isConnected"
        FROM "port"."PortVisitor" v
        INNER JOIN "user"."Profile" u ON u."id" = v."profileId"
        WHERE
          ${
            dataType === 'raw'
              ? Prisma.sql`v."portRawDataUnLocode" = ${unLocode}`
              : Prisma.sql`v."portUnLocode" = ${unLocode}`
          }
          AND NOT EXISTS (
            SELECT 1
            FROM "network"."BlockedProfile" b
            WHERE (
              (b."blockerId" = ${selfProfileId}::uuid AND b."blockedId" = u."id")
              OR
              (b."blockerId" = u."id" AND b."blockedId" = ${selfProfileId}::uuid)
            )
            LIMIT 1
          )
        OFFSET ${page * pageSize}
        LIMIT ${pageSize}
      `,
    ]);

    const portVisitorFetchForClientResult: PeopleI[] = [];

    if (visitorResultTemp?.length) {
      portVisitorFetchForClientResult.push(
        ...visitorResultTemp.map((visitor) => ({
          id: visitor.id,
          name: visitor.name,
          avatar: visitor.avatar,
          designation: (visitor.designationAlternativeId
            ? { id: visitor.designationAlternativeId, name: visitor.designationText, dataType: 'master' }
            : {
                id: visitor.designationRawDataId,
                name: visitor.designationText,
                dataType: 'raw',
              }) as DesignationNestedClientI,
          entity: (visitor.entityId
            ? { id: visitor.entityId, name: visitor.entityText, dataType: 'master' }
            : { id: visitor.entityRawDataId, name: visitor.entityText, dataType: 'raw' }) as EntityNestedExternalI,
          isConnected: visitor.id === selfProfileId ? null : visitor.isConnected,
        })),
      );
    }

    return { total: Number(visitorCountResult?.[0]?.total), data: portVisitorFetchForClientResult };
  },
  createOne: async (state: FastifyStateI, params: PortVisitorCreateOneParamsI): Promise<void> => {
    const filters: Prisma.PortVisitorWhereInput = {
      profileId: state.profileId,
      portUnLocode: params.portUnLocode,
    };

    const existingPortVisitorResult = await prismaPG.portVisitor.findFirst({
      where: filters,
      select: {
        id: true,
      },
    });

    if (existingPortVisitorResult) {
      throw new AppError('PRTVSR003');
    }

    const portVisitorResult = await prismaPG.portVisitor.create({
      data: {
        profileId: state.profileId,
        portUnLocode: params.portUnLocode,
      },
      select: {
        id: true,
      },
    });

    if (!portVisitorResult) {
      throw new AppError('PRTVSR002');
    }

    return;
  },
  deleteOne: async (state: FastifyStateI, filtersP: PortVisitorDeleteOneParamsI): Promise<void> => {
    const filters: Prisma.PortVisitorWhereInput = {
      profileId: state.profileId,
      portUnLocode: filtersP.portUnLocode,
    };

    const existingVisitorResult = await prismaPG.portVisitor.findFirst({
      where: filters,
      select: { id: true },
    });

    if (!existingVisitorResult) {
      throw new AppError('PRTVSR001');
    }

    const deletedVisitorResult = await prismaPG.portVisitor.delete({
      where: { id: existingVisitorResult.id },
      select: { id: true },
    });

    if (!deletedVisitorResult) {
      throw new AppError('PRTVSR007');
    }

    return;
  },
};
