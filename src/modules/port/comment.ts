import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type {
  ScrapBookCommentCreateOneResultI,
  ScrapBookCommentFetchManyResultI,
  ScrapBookCommentFetchForClientI,
  ProfileItemI,
  ScrapBookCommentTempResultItemI,
} from '@interfaces/port/comment';
import { Prisma, PostStatusE } from '@prisma/postgres';
import {
  ScrapBookCommentCreateOneI,
  ScrapBookCommentFetchManyI,
  ScrapBookCommentFetchRepliesI,
} from '@schemas/port/comment';
import { omit } from '@utils/data/object';

export const ScrapBookCommentModule = {
  fetchMany: async (
    state: FastifyStateI,
    { scrapBookPostId, cursorId, pageSize }: ScrapBookCommentFetchManyI,
  ): Promise<ScrapBookCommentFetchManyResultI> => {
    const selfProfileId = state.profileId;
    const filters: Prisma.ScrapBookCommentWhereInput = {
      scrapBookPostId,
      parentCommentId: null,
      status: PostStatusE.ACTIVE,
      NOT: {
        Profile: {
          OR: [
            {
              BlockedByProfile: {
                some: {
                  blockerId: selfProfileId,
                },
              },
            },
            {
              BlockedProfile: {
                some: {
                  blockedId: selfProfileId,
                },
              },
            },
          ],
        },
      },
    };

    if (typeof cursorId === 'number' && cursorId > 0) {
      filters.cursorId = {
        lt: BigInt(cursorId),
      };
    }

    const replyFilters: Prisma.ScrapBookCommentWhereInput = {
      status: PostStatusE.ACTIVE,
      NOT: {
        Profile: {
          OR: [
            {
              BlockedByProfile: {
                some: {
                  blockerId: selfProfileId,
                },
              },
            },
            {
              BlockedProfile: {
                some: {
                  blockedId: selfProfileId,
                },
              },
            },
          ],
        },
      },
    };

    const [commentsCountResult, commentTempResult] = await Promise.all([
      prismaPG.scrapBookComment.count({
        where: omit(filters, ['cursorId']),
      }),
      prismaPG.scrapBookComment.findMany({
        select: {
          id: true,
          text: true,
          cursorId: true,
          Profile: {
            select: {
              id: true,
              avatar: true,
              name: true,
              designationText: true,
              designationAlternativeId: true,
              designationRawDataId: true,
              entityText: true,
              entityId: true,
              entityRawDataId: true,
            },
          },
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              Replies: {
                where: replyFilters,
              },
            },
          },
          Replies: {
            select: {
              id: true,
              text: true,
              cursorId: true,
              Profile: {
                select: {
                  id: true,
                  avatar: true,
                  name: true,
                  designationText: true,
                  designationAlternativeId: true,
                  designationRawDataId: true,
                  entityText: true,
                  entityId: true,
                  entityRawDataId: true,
                },
              },
              createdAt: true,
              updatedAt: true,
            },
            where: replyFilters,
            take: 2,
            orderBy: {
              cursorId: 'desc',
            },
          },
        },
        where: filters,
        take: pageSize,
        orderBy: {
          cursorId: 'desc',
        },
      }),
    ]);

    const transformProfile = (profile: ProfileItemI) => ({
      id: profile.id,
      name: profile.name,
      avatar: profile.avatar,
      designation: profile.designationAlternativeId
        ? {
            id: profile.designationAlternativeId,
            name: profile.designationText || '',
            dataType: 'master' as const,
          }
        : profile.designationRawDataId
          ? {
              id: profile.designationRawDataId,
              name: profile.designationText || '',
              dataType: 'raw' as const,
            }
          : null,
      entity: profile.entityId
        ? {
            id: profile.entityId,
            name: profile.entityText || '',
            dataType: 'master' as const,
          }
        : profile.entityRawDataId
          ? {
              id: profile.entityRawDataId,
              name: profile.entityText || '',
              dataType: 'raw' as const,
            }
          : null,
    });

    const transformComment = (
      item: ScrapBookCommentTempResultItemI,
    ): Omit<ScrapBookCommentFetchForClientI, 'replies'> => ({
      id: item.id,
      text: item.text,
      cursorId: Number(item.cursorId.toString()),
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      Profile: transformProfile(item.Profile),
    });

    const commentResult: ScrapBookCommentFetchForClientI[] = commentTempResult.map((item) => ({
      ...transformComment(item),
      repliesCount: item._count.Replies,
      replies: (item.Replies || []).map(
        (reply): ScrapBookCommentFetchForClientI => ({
          ...transformComment(reply),
          replies: [],
        }),
      ),
    }));

    const lastComment = commentResult[commentResult.length - 1];
    const resultCursorId =
      commentResult.length > 0 && lastComment ? lastComment.cursorId : typeof cursorId === 'number' ? cursorId : null;

    return {
      comments: commentResult,
      total: commentsCountResult,
      cursorId: resultCursorId,
    };
  },

  fetchReplies: async (
    state: FastifyStateI,
    { scrapBookPostId, parentCommentId, cursorId, pageSize }: ScrapBookCommentFetchRepliesI,
  ): Promise<ScrapBookCommentFetchManyResultI> => {
    const selfProfileId = state.profileId;
    const filters: Prisma.ScrapBookCommentWhereInput = {
      scrapBookPostId,
      parentCommentId,
      status: PostStatusE.ACTIVE,
      NOT: {
        Profile: {
          OR: [
            {
              BlockedByProfile: {
                some: {
                  blockerId: selfProfileId,
                },
              },
            },
            {
              BlockedProfile: {
                some: {
                  blockedId: selfProfileId,
                },
              },
            },
          ],
        },
      },
    };

    if (typeof cursorId === 'number' && cursorId > 0) {
      filters.cursorId = {
        lt: BigInt(cursorId),
      };
    }

    const [commentsCountResult, commentTempResult] = await Promise.all([
      prismaPG.scrapBookComment.count({
        where: omit(filters, ['cursorId']),
      }),
      prismaPG.scrapBookComment.findMany({
        select: {
          id: true,
          text: true,
          cursorId: true,
          Profile: {
            select: {
              id: true,
              avatar: true,
              name: true,
              designationText: true,
              designationAlternativeId: true,
              designationRawDataId: true,
              entityText: true,
              entityId: true,
              entityRawDataId: true,
            },
          },
          createdAt: true,
          updatedAt: true,
        },
        where: filters,
        take: pageSize,
        orderBy: {
          cursorId: 'desc',
        },
      }),
    ]);

    const transformProfile = (profile: ProfileItemI) => ({
      id: profile.id,
      name: profile.name,
      avatar: profile.avatar,
      designation: profile.designationAlternativeId
        ? {
            id: profile.designationAlternativeId,
            name: profile.designationText || '',
            dataType: 'master' as const,
          }
        : profile.designationRawDataId
          ? {
              id: profile.designationRawDataId,
              name: profile.designationText || '',
              dataType: 'raw' as const,
            }
          : null,
      entity: profile.entityId
        ? {
            id: profile.entityId,
            name: profile.entityText || '',
            dataType: 'master' as const,
          }
        : profile.entityRawDataId
          ? {
              id: profile.entityRawDataId,
              name: profile.entityText || '',
              dataType: 'raw' as const,
            }
          : null,
    });

    const transformComment = (item: ScrapBookCommentTempResultItemI): ScrapBookCommentFetchForClientI => ({
      id: item.id,
      text: item.text,
      cursorId: Number(item.cursorId.toString()),
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      Profile: transformProfile(item.Profile),
      replies: [],
    });

    const commentResult: ScrapBookCommentFetchForClientI[] = commentTempResult.map(transformComment);

    const lastComment = commentResult[commentResult.length - 1];
    const resultCursorId =
      commentResult.length > 0 && lastComment ? lastComment.cursorId : typeof cursorId === 'number' ? cursorId : null;

    return {
      comments: commentResult,
      total: commentsCountResult,
      cursorId: resultCursorId,
    };
  },

  createOne: async (
    state: FastifyStateI,
    { parentCommentId, scrapBookPostId, text }: ScrapBookCommentCreateOneI,
  ): Promise<ScrapBookCommentCreateOneResultI> => {
    const [scrapBookPostResult, parentCommentResult] = await Promise.all([
      prismaPG.scrapBookPost.findUnique({
        where: { id: scrapBookPostId, status: PostStatusE.ACTIVE },
        select: { id: true },
      }),
      parentCommentId
        ? prismaPG.scrapBookComment.findUnique({
            where: { id: parentCommentId, scrapBookPostId, status: PostStatusE.ACTIVE },
            select: { id: true, parentCommentId: true },
          })
        : null,
    ]);

    if (!scrapBookPostResult) {
      throw new AppError('SCBKCMT001');
    }

    if (parentCommentId && !parentCommentResult) {
      throw new AppError('SCBKCMT001');
    }

    if (parentCommentResult?.parentCommentId) {
      throw new AppError('SCBKCMT001');
    }

    const commentInput: Prisma.ScrapBookCommentUncheckedCreateInput = {
      text: text.trim(),
      profileId: state.profileId!,
      scrapBookPostId,
      status: PostStatusE.ACTIVE,
    };

    if (parentCommentResult) {
      commentInput.parentCommentId = parentCommentResult.id;
    }

    const [_scrapBookPostResult, commentResult] = await prismaPG.$transaction([
      prismaPG.scrapBookPost.update({
        data: {
          commentCount: {
            increment: 1,
          },
        },
        where: {
          id: scrapBookPostId,
        },
        select: {
          id: true,
        },
      }),
      prismaPG.scrapBookComment.create({
        select: { id: true, cursorId: true },
        data: commentInput,
      }),
    ]);

    return {
      id: commentResult.id,
      cursorId: Number(commentResult.cursorId.toString()),
    };
  },

  deleteOne: async (
    state: FastifyStateI,
    filters: Pick<Prisma.ScrapBookCommentWhereUniqueInput, 'id'>,
  ): Promise<void> => {
    const commentResult = await prismaPG.scrapBookComment.findFirst({
      select: {
        id: true,
        parentCommentId: true,
        scrapBookPostId: true,
        ScrapBookPost: {
          select: {
            profileId: true,
          },
        },
      },
      where: {
        OR: [{ profileId: state.profileId }, { ScrapBookPost: { profileId: state.profileId } }],
        id: filters.id,
        status: PostStatusE.ACTIVE,
      },
    });

    if (!commentResult) {
      throw new AppError('SCBKCMT001');
    }

    await prismaPG.$transaction(async (txn) => {
      let countCommentAndReplies = 1;

      if (!commentResult.parentCommentId) {
        const countRepliesResult = await txn.scrapBookComment.count({
          where: {
            parentCommentId: commentResult.id,
            status: PostStatusE.ACTIVE,
          },
        });

        if (countRepliesResult > 0) {
          countCommentAndReplies += countRepliesResult;
          await txn.scrapBookComment.updateMany({
            data: { status: PostStatusE.DELETED },
            where: {
              parentCommentId: commentResult.id,
              status: PostStatusE.ACTIVE,
            },
          });
        }
      }

      await txn.scrapBookComment.update({
        data: { status: PostStatusE.DELETED },
        where: { id: commentResult.id },
      });

      await txn.scrapBookPost.update({
        data: {
          commentCount: {
            decrement: countCommentAndReplies,
          },
        },
        where: {
          id: commentResult.scrapBookPostId,
        },
      });
    });
  },
};
