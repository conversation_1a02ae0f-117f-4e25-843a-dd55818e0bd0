import { prismaPG } from '@config/db';
import type { ObjStrI } from '@interfaces/common/data';
import type { FastifyStateI } from '@interfaces/common/declaration';
import { Prisma } from '@prisma/postgres';
import { PortContributionFetchOneParamsI, PortContributionUpsertOneParamsI } from '@schemas/port/contribution';
import { PortModule } from './port';
import AppError from '@classes/AppError';
import { KeyContributionParamsI } from '@interfaces/common/contribution';
import { isPrimitiveData } from '@utils/data/data';

export const PortContributionModule = {
  fetch: async (state: FastifyStateI, params: PortContributionFetchOneParamsI) => {
    await PortModule.fetchByUnLocode({ unLocode: params.unLocode, dataType: params.dataType });
    const filters: Prisma.PortContributionWhereInput = {
      profileId: state.profileId,
    };
    if (params.dataType === 'master') {
      filters.portUnLocode = params.unLocode;
    } else if (params.dataType === 'raw') {
      filters.portRawDataUnLocode = params.unLocode;
    }
    const portContributionsResult = await prismaPG.portContribution.findMany({
      select: {
        label: true,
        value: true,
      },
      where: filters,
      orderBy: {
        createdAt: 'desc',
      },
    });
    return portContributionsResult;
  },
  upsertOne: async (state: FastifyStateI, params: PortContributionUpsertOneParamsI): Promise<void> => {
    await PortModule.fetchByUnLocode({ unLocode: params.unLocode, dataType: params.dataType });
    const contributionsMap: KeyContributionParamsI = params.contributions.reduce((acc, curr) => {
      acc[curr.label] = curr.value;
      return acc;
    }, {} as KeyContributionParamsI);

    const filters: Prisma.PortContributionWhereInput = {
      profileId: state.profileId,
      label: {
        in: Object.keys(contributionsMap),
      },
    };
    if (params.dataType === 'master') {
      filters.portUnLocode = params.unLocode;
    } else if (params.dataType === 'raw') {
      filters.portRawDataUnLocode = params.unLocode;
    }
    const existingPortContributionsResult = await prismaPG.portContribution.findMany({
      select: {
        id: true,
        label: true,
      },
      where: filters,
    });
    const existingContributionLabelIdMap: ObjStrI = existingPortContributionsResult.reduce((acc, curr) => {
      acc[curr.label] = curr.id;
      return acc;
    }, {} as ObjStrI);

    const createInput: Prisma.PortContributionUncheckedCreateInput[] = [];

    const updateInputFilter: {
      input: Prisma.PortContributionUpdateInput;
      filter: Prisma.PortContributionWhereUniqueInput;
    }[] = [];

    Object.keys(contributionsMap).forEach((labelKey) => {
      const contributionValue = contributionsMap[labelKey];

      const existingContributionId = existingContributionLabelIdMap?.[labelKey];
      if (existingContributionId) {
        updateInputFilter.push({
          input: {
            value: isPrimitiveData(contributionValue) ? String(contributionValue) : JSON.stringify(contributionValue),
          },
          filter: {
            id: existingContributionId,
          },
        });
      } else {
        const createInputItem: Prisma.PortContributionUncheckedCreateInput = {
          label: labelKey,
          value: isPrimitiveData(contributionValue) ? String(contributionValue) : JSON.stringify(contributionValue),
          profileId: state.profileId,
        };
        if (params.dataType === 'master') {
          createInputItem.portUnLocode = params.unLocode;
        } else if (params.dataType === 'raw') {
          createInputItem.portRawDataUnLocode = params.unLocode;
        }
        createInput.push(createInputItem);
      }
    });
    let countUpserted: number = 0;
    if (createInput?.length) {
      countUpserted = (await prismaPG.portContribution.createMany({ data: createInput })).count;
    }
    if (updateInputFilter?.length) {
      const updatedPortContributionResult = await Promise.all(
        updateInputFilter.map((updateInputFilterItem) =>
          prismaPG.portContribution.update({
            data: updateInputFilterItem.input,
            where: updateInputFilterItem.filter,
            select: {
              id: true,
            },
          }),
        ),
      );
      updatedPortContributionResult.forEach((item) => {
        if (item.id) {
          ++countUpserted;
        }
      });
    }
    if (countUpserted !== Object.keys(contributionsMap).length) {
      throw new AppError('PRTCNB006');
    }
    return;
  },
};
