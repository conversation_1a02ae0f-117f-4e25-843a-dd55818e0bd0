import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { AuthLoginParamsI, AuthRegisterParamsI } from '@schemas/auth/auth';
import { comparePassword, hashPassword } from '@utils/cryptography/password';
import SessionModule from './session';
import { Prisma, Profile, ProfileMeta, ProfileStatus, Session } from '@prisma/postgres';
import Vendor from '@modules/vendor';
import User from '@modules/user';
import { RequireSome } from '@interfaces/common/data';
import { omit } from '@utils/data/object';
import { AuthLoginResultI, AuthRegisterResultI } from '@interfaces/auth/auth';
import AppModule from '@modules/app';
import Google from '@navicater/vendor-google';
import { ProfileUpdateOneDataI } from '@interfaces/user/profile';
import CommunicationModule from '@modules/communication';

const AuthModule = {
  register: async (params: AuthRegisterParamsI): Promise<AuthRegisterResultI> => {
    const profileSelect: Prisma.ProfileSelect = {
      id: true,
    };
    if (params.type !== 'EMAIL_PASSWORD') {
      throw new AppError('AUTH008');
    }

    const existingProfile: Pick<Profile, 'id'> = await prismaPG.profile.findUnique({
      where: {
        email: params.email!,
      },
      select: profileSelect,
    });
    if (!existingProfile) {
      throw new AppError('AUTH005');
    }
    const foundUsernameProfile = await prismaPG.profile.findFirst({
      where: {
        username: params.username!,
      },
      select: {
        id: true,
      },
    });
    if (foundUsernameProfile) {
      throw new AppError('AUTH006');
    }
    const encryptedPassword = await hashPassword(params.password);

    const [profile, _profileStatus, _profileMeta]: [
      Pick<Profile, 'id'>,
      Pick<ProfileStatus, 'profileId'>,
      Pick<ProfileMeta, 'profileId'>,
    ] = await prismaPG.$transaction(async (txn) => {
      const profileTemp = await txn.profile.create({
        data: {
          email: params.email,
          password: encryptedPassword,
          username: params.username,
        },
        select: profileSelect,
      });
      const [profileStatusTemp, profileMetaTemp] = await Promise.all([
        txn.profileStatus.create({
          data: { profileId: profileTemp.id, isPasswordSaved: true } as Prisma.ProfileStatusUncheckedCreateInput,
          select: {
            profileId: true,
          },
        }),
        txn.profileMeta.create({ data: { profileId: profileTemp.id }, select: { profileId: true } }),
      ]);
      return [profileTemp, profileStatusTemp, profileMetaTemp];
    });

    const authResult: AuthRegisterResultI = {
      profileId: profile.id,
    };
    return authResult as AuthRegisterResultI;
  },
  login: async (params: AuthLoginParamsI): Promise<AuthLoginResultI> => {
    // The columns to be fetched in the select query for profile
    const profileSelect: Prisma.ProfileSelect = {
      id: true,
      email: true,
      username: true,
      googleSub: true,
      avatar: true,
    };
    // The columns to be fetched in the select query for profileStatus
    const profileStatusSelect: Prisma.ProfileStatusSelect = {
      isPasswordSaved: true,
      isEmailVerified: true,
      isPersonalDetailsSaved: true,
      isWorkDetailsSaved: true,
    };
    let profile: RequireSome<Profile, 'id' | 'email' | 'username'>;
    let profileStatus: Pick<
      ProfileStatus,
      'isPasswordSaved' | 'isPersonalDetailsSaved' | 'isWorkDetailsSaved' | 'isEmailVerified'
    >;
    let _profileMeta: Pick<ProfileMeta, 'profileId'>;
    let existingSessionResult: Pick<Session, 'id'>;
    // If the login type is GOOGLE then we'll execute this block
    if (params.type === 'GOOGLE') {
      // Fetches the instance of GOOGLE vendor for verification of the googleIdToken
      const { instance } = await Vendor.VendorModule.getVendor('GOOGLE');
      let payloadResult;
      try {
        // If googleIdToken isn't verified then we'll throw an error
        payloadResult = await (instance as Google).verifyGoogleIdToken(params.googleToken);
      } catch (_error) {
        throw new AppError('AUTH010');
      }
      //
      profile = await prismaPG.profile.findUnique({
        where: {
          email: payloadResult.email!,
        },
        select: profileSelect,
      });
      // If the profile doesn't exist then we'll create an entry in Profile & ProfileStatus tables
      if (!profile) {
        [profile, profileStatus, _profileMeta] = await prismaPG.$transaction(async (txn) => {
          const profileTemp = await txn.profile.create({
            data: {
              email: payloadResult.email,
              googleSub: payloadResult.sub,
              avatar: payloadResult.picture,
            },
            select: profileSelect,
          });
          const profileStatusTemp = await txn.profileStatus.create({
            data: { profileId: profileTemp.id, isEmailVerified: true } as Prisma.ProfileStatusUncheckedCreateInput,
            select: profileStatusSelect,
          });
          const profileMetaTemp = await txn.profileMeta.create({
            data: { profileId: profileTemp.id },
            select: { profileId: true },
          });
          return [profileTemp, profileStatusTemp, profileMetaTemp];
        });
      } else {
        // If the profile with the Google sign in's email exists & if the googleSub doesn't exist then we'll set the googleSub
        if (!profile?.googleSub?.length) {
          const toUpdateProfileParams: ProfileUpdateOneDataI = { googleSub: payloadResult.sub };
          // If the profile doesn't has an avatar we'll set the payload's picture
          if (!profile?.avatar?.length) {
            toUpdateProfileParams.avatar = payloadResult.picture;
          }
          profile = await User.ProfileModule.updateOne(toUpdateProfileParams, { id: profile.id }, profileSelect);
        }
        // If payload's sub isn't the same as the Profile's googleSub
        if (profile.googleSub !== payloadResult.sub) {
          throw new AppError('AUTH004');
        }

        [profileStatus, existingSessionResult] = await Promise.all([
          // We'll fetch the ProfileStatus
          User.ProfileStatusModule.fetchById({ profileId: profile.id }, profileStatusSelect),
          // We'll fetch the Session
          prismaPG.session.findFirst({
            select: {
              id: true,
            },
            where: {
              profileId: profile.id,
              platformId: params.platform,
              isActive: true,
            },
          }),
        ]);
        // If the ProfileStatus' isEmailVerified is false then we'll set it to true
        if (!profileStatus.isEmailVerified) {
          profileStatus = await User.ProfileStatusModule.updateOne(
            { isEmailVerified: true },
            { profileId: profile.id },
          );
        }
      }
    } else {
      profile = await prismaPG.profile.findUnique({
        where: {
          email: params.email!,
        },
        select: {
          ...omit(profileSelect, ['googleSub']),
          password: true,
        },
      });
      if (!profile) {
        throw new AppError('AUTH002');
      }
      profileStatus = await User.ProfileStatusModule.fetchById({ profileId: profile.id! }, profileStatus);
      if (!profile) {
        throw new AppError('AUTH002');
      }
      if (!profileStatus.isPasswordSaved) {
        throw new AppError('AUTH003');
      }
      const isEqual = await comparePassword(profile.password, params.password);
      if (!isEqual) {
        throw new AppError('AUTH001');
      }
    }
    // Fetches appVersion based on the user's versionNo & platform
    const appVersionResult = await AppModule.AppVersionModule.fetchByIdVersionPlatform({
      versionNo: params.versionNo,
      platformId: params.platform,
    });

    if (appVersionResult.platformId !== params.platform) {
      throw new AppError('AUTH009');
    }
    const [sessionResult, _deletedExistingSessionResult] = await prismaPG.$transaction(async (txn) => {
      const newSession = await SessionModule.createOne(txn, {
        ipAddress: params.ip,
        profileId: profile.id,
        appVersionId: appVersionResult.id,
        deviceId: params.deviceId,
        platformId: params.platform,
      });

      if (existingSessionResult?.id) {
        const sessionExists = await txn.session.findUnique({
          where: { id: existingSessionResult.id },
          select: { id: true },
        });

        if (sessionExists) {
          await SessionModule.deleteOne(txn, { id: existingSessionResult.id });
        }
      }

      return [newSession, existingSessionResult?.id || null];
    });
    await Promise.all([
      CommunicationModule.SessionModule.createOne({
        deviceToken: params.deviceToken,
        opr: 'CREATE',
        isActive: true,
        profileId: profile.id,
        sessionId: sessionResult.id,
      }),
      existingSessionResult?.id
        ? CommunicationModule.SessionModule.deleteOne({
            opr: 'DELETE',
            sessionId: existingSessionResult.id,
          })
        : null,
    ]);
    const authResult: AuthLoginResultI = {
      username: profile?.username,
      name: profile?.name,
      isUsernameSaved: Boolean(profile?.username?.trim()?.length),
      email: profile.email,
      profileId: profile.id,
      designationText: profile.designationText,
      entityText: profile.entityText,
      avatar: profile.avatar,
      isEmailVerified: profileStatus.isEmailVerified,
      isPersonalDetailsSaved: profileStatus.isPersonalDetailsSaved,
      isWorkDetailsSaved: profileStatus.isWorkDetailsSaved,
      token: sessionResult.id,
    };
    return authResult as AuthLoginResultI;
  },
  logout: async (sessionId: string): Promise<void> => {
    const sessionResult = await SessionModule.updateOne({ isActive: false }, { id: sessionId });
    if (!sessionResult) {
      throw new AppError('AUTH022');
    }
    await CommunicationModule.SessionModule.deleteOne({
      opr: 'DELETE',
      sessionId: sessionResult.id,
    });
  },
};
export default AuthModule;
