import { HttpStatus } from '@consts/common/api/status';
import { AppErrorJSONI } from '@interfaces/common/api/appError';
import { ErrorCodeI, ErrorCodes, ErrorCodeValueI } from '@consts/common/error/errorCodes';
import { ObjUnknownI } from '@interfaces/common/data';
import { ZodError } from 'zod';
import { getError } from '@utils/errors/schema';

export default class AppError {
  private _status: HttpStatus;
  private _errorCode: ErrorCodeI;
  private _message: string;
  private _data?: ObjUnknownI;
  constructor(_errorCode: ErrorCodeI, _data?: ObjUnknownI | ZodError | string) {
    this._errorCode = _errorCode;
    const ErrorCodeValue: ErrorCodeValueI = ErrorCodes[_errorCode];
    this._status = ErrorCodeValue.status;
    this._message = ErrorCodeValue.message;
    this._data = { error: _data instanceof ZodError ? getError(_data) : _data };
  }
  get errorCode(): ErrorCodeI {
    return this._errorCode;
  }
  get status(): HttpStatus {
    return this._status;
  }
  get message(): string {
    return this._message;
  }
  get data(): ObjUnknownI | undefined {
    return this._data;
  }
  get json(): AppErrorJSONI {
    return {
      code: this._errorCode,
      message: this.message,
      ...this._data,
    };
  }
}
