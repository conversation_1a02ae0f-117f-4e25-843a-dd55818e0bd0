import { FastifyInstance } from 'fastify';
import authRoutes from './v1/auth';
import { authNoTokenMiddleware } from 'middlewares/authMiddleware';
import healthRoutes from './v1/health';

const unsecureRoutes = (fastify: FastifyInstance): void => {
  fastify.addHook('preHandler', authNoTokenMiddleware);
  fastify.register(authRoutes);
  fastify.register(healthRoutes);
};

export default unsecureRoutes;
