import type { FastifyInstance, FastifyReply } from 'fastify';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import Auth from '@modules/auth';
import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';

const logoutRoutes = (fastify: FastifyInstance): void => {
  fastify.delete('/backend/api/v1/auth/logout', async (request: FastifyRequestI, reply: FastifyReply) => {
    try {
      await Auth.AuthModule.logout(request.sessionId!);
      reply.status(HttpStatus.NO_CONTENT);
    } catch (error) {
      throw new AppError('AUTH022', error);
    }
  });
};

export default logoutRoutes;
