import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import ServiceModule from '@modules/storage';
import { PresignedURLsSchema } from '@schemas/storage/storage';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const storageChildRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/storage/presigned-url', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { data, error } = PresignedURLsSchema.safeParse(request.body);

    if (error) {
      throw new AppError('STRG003', error);
    }
    const presignedURLsResult = await ServiceModule.StorageModule.presignedURLsBulk(data);
    reply.status(HttpStatus.CREATED).send(presignedURLsResult);
  });
};

export default storageChildRoutes;
