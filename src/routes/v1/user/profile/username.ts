import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import User from '@modules/user';
import { UpdateUsernameParamsSchema } from '@schemas/user/profile';
import { FastifyInstance, FastifyReply } from 'fastify';

const usernameRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/user/profile/username', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = UpdateUsernameParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PFL008', queryError);
    }
    await User.ProfileModule.isUnusedUsername({ username: queryData.username! });
    reply.status(HttpStatus.OK);
  });

  fastify.patch('/backend/api/v1/user/profile/username', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = UpdateUsernameParamsSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('PFL008', { error: bodyError.errors });
    }
    const result = await User.ProfileModule.updateOne(bodyData, {
      id: request.profileId,
    });
    reply.status(HttpStatus.OK).send(result);
  });
};

export default usernameRoutes;
