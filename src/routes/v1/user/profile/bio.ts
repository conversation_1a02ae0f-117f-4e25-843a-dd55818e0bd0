import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import User from '@modules/user';
import { RouteParamsSchema } from '@schemas/common/common';
import { UpdateBioParamsI, UpdateBioParamsSchema } from '@schemas/user/profile';
import { getError } from '@utils/errors/schema';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const bioRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/user/profile/bio/:id', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error, data } = RouteParamsSchema.safeParse(request.params);
    if (error) {
      throw new AppError('PFL011', getError(error));
    }
    const result = await User.ProfileModule.fetchBio(data);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.patch('/backend/api/v1/user/profile/bio', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = UpdateBioParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('PFL010', getError(error));
    }
    const result = await User.ProfileModule.updateBio(data as UpdateBioParamsI, request.profileId);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default bioRoutes;
