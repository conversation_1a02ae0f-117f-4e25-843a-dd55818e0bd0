import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import User from '@modules/user';
import { RouteParamsSchema } from '@schemas/common/common';
import { getError } from '@utils/errors/schema';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const dataRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/user/profile/data/:id', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error, data } = RouteParamsSchema.safeParse(request.params);
    if (error) {
      throw new AppError('PFL011', getError(error));
    }
    const result = await User.ProfileModule.fetchData(data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default dataRoutes;
