import { HttpStatus } from '@consts/common/api/status';
import { DegreeModuleFetchsertParamsI } from '@interfaces/company/degree';
import Company from '@modules/company';
import { DegreeOptionsFetchSchema, DegreeNameSchema } from '@schemas/company/degree';

import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const degreeRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/company/degree/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const query = DegreeOptionsFetchSchema.parse(request.query);
    const result = await Company.DegreeModule.fetchForClient(query.search, pick(query, ['page', 'pageSize']));
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/company/degree/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const body = DegreeNameSchema.parse(request.body);
    const result = await Company.DegreeModule.fetchsert(body as DegreeModuleFetchsertParamsI);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default degreeRoutes;
