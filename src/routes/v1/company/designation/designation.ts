import { HttpStatus } from '@consts/common/api/status';
import { DesignationModuleFetchsertParamsI } from '@interfaces/company/designation';
import Company from '@modules/company';
import { DesignationOptionsFetchSchema, DesignationNameSchema } from '@schemas/company/designation';
import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const designationRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/company/designation/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const query = DesignationOptionsFetchSchema.parse(request.query);
      const result = await Company.DesignationModule.fetchForClient(query.search, pick(query, ['page', 'pageSize']));
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/company/designation/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const body = DesignationNameSchema.parse(request.body);
      const result = await Company.DesignationModule.fetchsert(body as DesignationModuleFetchsertParamsI);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default designationRoutes;
