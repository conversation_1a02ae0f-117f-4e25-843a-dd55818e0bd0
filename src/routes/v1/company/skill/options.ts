import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { SkillModuleFetchsertParamsI } from '@interfaces/company/skill';
import Company from '@modules/company';
import { SkillOptionsFetchSchema, SkillNameSchema } from '@schemas/company/skill';

import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const skillRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/company/skill/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = SkillOptionsFetchSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('SKL006', { error: queryError });
    }
    const result = await Company.SkillModule.fetchForClient(queryData.search, pick(queryData, ['page', 'pageSize']));
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/company/skill/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error: bodyError, data: bodyData } = SkillNameSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('SKL005', { error: bodyError });
    }
    const result = await Company.SkillModule.fetchsert(bodyData as SkillModuleFetchsertParamsI);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default skillRoutes;
