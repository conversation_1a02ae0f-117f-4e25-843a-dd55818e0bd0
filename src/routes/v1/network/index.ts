import { FastifyInstance } from 'fastify';
import blockingRoutes from './blocking';
import connectionRoutes from './connection';
import followRoutes from './follow';
import requestRoutes from './request';

const networkRoutes = (fastify: FastifyInstance): void => {
  fastify.register(blockingRoutes);
  fastify.register(connectionRoutes);
  fastify.register(followRoutes);
  fastify.register(requestRoutes);
};

export default networkRoutes;
