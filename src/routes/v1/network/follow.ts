import { FastifyInstance, FastifyReply } from 'fastify';
import { HttpStatus } from '@consts/common/api/status';
import Network from '@modules/network';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import { FollowOneParamsSchema } from '@schemas/network/follow';
import AppError from '@classes/AppError';
import { ProfileIdCursorPaginationSchema } from '@schemas/common/common';

const followRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/network/follow', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = FollowOneParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('FLW007', error);
    }
    const result = await Network.FollowModule.followOne(request, data);
    reply.status(HttpStatus.CREATED).send(result);
  });

  fastify.delete('/backend/api/v1/network/unfollow', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = FollowOneParamsSchema.safeParse(request.query);
    if (error) {
      throw new AppError('FLW008');
    }
    await Network.FollowModule.unfollowOne(request, data);
    reply.status(HttpStatus.NO_CONTENT);
  });

  fastify.get('/backend/api/v1/network/followers', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = ProfileIdCursorPaginationSchema.safeParse(request.query);
    if (error) {
      throw new AppError('FLW008', error);
    }
    const result = await Network.FollowModule.fetchManyFollowers(request, data);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/network/followings', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = ProfileIdCursorPaginationSchema.safeParse(request.query);
    if (error) {
      throw new AppError('FLW008', error);
    }
    const result = await Network.FollowModule.fetchManyFollowings(request, data);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/network/follow-status', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = FollowOneParamsSchema.safeParse(request.query);
    if (error) {
      throw new AppError('FLW008', error);
    }
    const result = await Network.FollowModule.followStatus(request, data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default followRoutes;
