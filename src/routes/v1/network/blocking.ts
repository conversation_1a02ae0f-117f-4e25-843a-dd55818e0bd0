import { FastifyInstance, FastifyReply } from 'fastify';
import { HttpStatus } from '@consts/common/api/status';
import Network from '@modules/network';
import { FastifyRequestI } from '@interfaces/common/declaration';
import { BlockingBlockOneSchema, BlockingFetchManySchema, BlockingUnblockOneSchema } from '@schemas/network/blocking';
import AppError from '@classes/AppError';

const blockingRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/network/block', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = BlockingBlockOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('BLOCK006');
    }
    const blockedProfileResult = await Network.BlockingModule.blockOne(request, bodyData);
    reply.status(HttpStatus.OK).send(blockedProfileResult);
  });

  fastify.post('/backend/api/v1/network/unblock', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = BlockingUnblockOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('BLOCK006');
    }
    await Network.BlockingModule.unblockOne(request, bodyData);
    reply.status(HttpStatus.OK);
  });

  fastify.get('/backend/api/v1/network/blocked-profiles', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = BlockingFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('BLOCK005');
    }
    const blockedProfilesResult = await Network.BlockingModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(blockedProfilesResult);
  });
};

export default blockingRoutes;
