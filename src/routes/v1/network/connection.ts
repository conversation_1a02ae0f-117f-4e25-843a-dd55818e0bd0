import { FastifyInstance, FastifyReply } from 'fastify';
import { HttpStatus } from '@consts/common/api/status';
import Network from '@modules/network';
import { FastifyRequestI } from '@interfaces/common/declaration';
import { ConnectionFetchManySchema, ConnectionSearchSchema } from '@schemas/network/connection';
import AppError from '@classes/AppError';
import { ProfileIdRouteParamsSchema } from '@schemas/common/common';

const connectionRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/network/connections', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ConnectionFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('CN002');
    }
    const connectionsResult = await Network.ConnectionModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(connectionsResult);
  });
  fastify.get('/backend/api/v1/network/connections/mutual', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ConnectionFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('CN002');
    }
    const connectionsResult = await Network.ConnectionModule.mutualFetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(connectionsResult);
  });

  fastify.get(
    '/backend/api/v1/network/connection/global-search',
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = ConnectionSearchSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('CN002', queryData);
      }
      const result = await Network.ConnectionModule.fetchManySearchGlobal(request, queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.delete(
    '/backend/api/v1/network/connection/:profileId',
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: paramsData, error: paramsError } = ProfileIdRouteParamsSchema.safeParse(request.params);
      if (paramsError) {
        throw new AppError('CN003');
      }
      await Network.ConnectionModule.removeOne(request, paramsData);
      reply.status(HttpStatus.NO_CONTENT);
    },
  );
};

export default connectionRoutes;
