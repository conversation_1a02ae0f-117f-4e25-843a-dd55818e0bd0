import { HttpStatus } from '@consts/common/api/status';
import {
  SubVesselTypeFetchForClientSchema,
  SubVesselTypeFetchsertI,
  SubVesselTypeFetchsertSchema,
} from '@schemas/ship/subVesselType';
import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import Ship from '@modules/ship';

const subVesselTypeRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/ship/sub-vessel-type/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const query = SubVesselTypeFetchForClientSchema.parse(request.query);
      const result = await Ship.SubVesselTypeModule.fetchForClient(
        pick(query, ['search']),
        pick(query, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );

  fastify.post(
    '/backend/api/v1/ship/sub-vessel-type/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const body = SubVesselTypeFetchsertSchema.parse(request.body);
      const result = await Ship.SubVesselTypeModule.fetchsert(body as SubVesselTypeFetchsertI);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default subVesselTypeRoutes;
