import { HttpStatus } from '@consts/common/api/status';
import { FuelTypeFetchForClientSchema, FuelTypeFetchsertI, FuelTypeFetchsertSchema } from '@schemas/ship/fuelType';
import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import Ship from '@modules/ship';

const fuelTypeRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/ship/fuel-type/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const query = FuelTypeFetchForClientSchema.parse(request.query);
    const result = await Ship.FuelTypeModule.fetchForClient(pick(query, ['search']), pick(query, ['page', 'pageSize']));
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/ship/fuel-type/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const body = FuelTypeFetchsertSchema.parse(request.body);
    const result = await Ship.FuelTypeModule.fetchsert(body as FuelTypeFetchsertI);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default fuelTypeRoutes;
