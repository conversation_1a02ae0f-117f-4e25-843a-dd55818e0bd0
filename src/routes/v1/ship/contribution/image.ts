import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { SavedMultipartFile } from '@fastify/multipart';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Ship from '@modules/ship';
import { ContributionImageFileI, ContributionMultipleImageFileSchema } from '@schemas/common/contribution';

import { ShipImageFetchForClientParamsSchema } from '@schemas/ship/contribution';
import { ShipImoClientSchema } from '@schemas/ship/ship';

import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply } from 'fastify';

const contributionImageRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/ship/contribution/image', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ShipImageFetchForClientParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('SHPCNB005', queryError);
    }
    const result = await Ship.ShipImageContributionModule.fetchForClient(
      queryData,
      pick(queryData, ['page', 'pageSize']),
    );
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/ship/contribution/image', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const files: SavedMultipartFile[] = await request.saveRequestFiles();
    if (!files?.length) {
      throw new AppError('SHPCNB009');
    }
    const { error: filesError } = ContributionMultipleImageFileSchema.safeParse(
      files.map(
        (file) =>
          ({
            mimetype: file.mimetype,
            size: file.file.bytesRead,
          }) as ContributionImageFileI,
      ),
    );
    if (filesError) {
      throw new AppError('SHPCNB008', { error: filesError });
    }
    const { error: bodyError, data: bodyData } = ShipImoClientSchema.safeParse(request.body);

    if (bodyError) {
      throw new AppError('SHPCNB007');
    }
    const result = await Ship.ShipImageContributionModule.uploadImagesBulk(request, {
      imo: bodyData.imo,
      dataType: bodyData.dataType,
      files,
    });
    reply.status(HttpStatus.OK).send(result);
  });
};

export default contributionImageRoutes;
