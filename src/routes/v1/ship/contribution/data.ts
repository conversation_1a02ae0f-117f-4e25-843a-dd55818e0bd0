import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Ship from '@modules/ship';
import { ShipContributionUpsertOneParamsSchema } from '@schemas/ship/contribution';
import { ShipImoClientSchema } from '@schemas/ship/ship';
import { FastifyInstance, FastifyReply } from 'fastify';

const contributionDataRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/ship/contribution/data', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ShipImoClientSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('SHPCNB005', queryError);
    }
    const result = await Ship.ShipContributionModule.fetch(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/ship/contribution/data', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = ShipContributionUpsertOneParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('SHPCNB007');
    }
    const result = await Ship.ShipContributionModule.upsertOne(request, data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default contributionDataRoutes;
