import { HttpStatus } from '@consts/common/api/status';
import { MainVesselTypeFetchForClientSchema, MainVesselTypeFetchsertSchema } from '@schemas/ship/mainVesselType';
import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import Ship from '@modules/ship';
import AppError from '@classes/AppError';

const mainVesselTypeRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/ship/main-vessel-type/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const query = MainVesselTypeFetchForClientSchema.parse(request.query);
      const result = await Ship.MainVesselTypeModule.fetchForClient(
        pick(query, ['search']),
        pick(query, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/ship/main-vessel-type/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error, data } = MainVesselTypeFetchsertSchema.safeParse(request.query);
      if (error) {
        throw new AppError('MVSLTP006');
      }
      const result = await Ship.MainVesselTypeModule.fetchsert(data);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default mainVesselTypeRoutes;
