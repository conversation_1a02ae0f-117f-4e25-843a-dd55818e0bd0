import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { ProfileCertificate } from '@prisma/postgres';
import type {
  ProfileCertificateCreateOneParamsI,
  ProfileCertificateUpdateOneParamsI,
} from '@interfaces/career/certificate';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import Career from '@modules/career';
import {
  ProfileCertificateFetchForClientSchema,
  ProfileCertificatePatchBodySchema,
  ProfileCertificatePostBodySchema,
} from '@schemas/career/certificate';
import { RouteParamsSchema } from '@schemas/common/common';

import type { FastifyInstance, FastifyReply } from 'fastify';

const profileCertificateRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/career/profile-certificates',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = ProfileCertificateFetchForClientSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('PFCRT006', queryError);
      }
      const result = await Career.CertificateModule.fetchForClient(queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get(
    '/backend/api/v1/career/profile-certificate/:id',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: paramsData, error: paramsError } = RouteParamsSchema.safeParse(request.params);
      if (paramsError) {
        throw new AppError('PFCRT009', paramsError);
      }
      const result = await Career.CertificateModule.fetchOneForInternalClient(paramsData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.delete(
    '/backend/api/v1/career/profile-certificate/:id',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: paramsData, error: paramsError } = RouteParamsSchema.safeParse(request.params);
      if (paramsError) {
        throw new AppError('PFCRT009', paramsError);
      }
      await Career.CertificateModule.deleteOne(request, paramsData as Pick<ProfileCertificate, 'id'>);
      reply.status(HttpStatus.NO_CONTENT);
    },
  );
  fastify.post(
    '/backend/api/v1/career/profile-certificate',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = ProfileCertificatePostBodySchema.safeParse(request.body);
      if (bodyError) {
        throw new AppError('PFCRT007', { error: bodyError });
      }
      const createParams: ProfileCertificateCreateOneParamsI = bodyData;

      const result = await Career.CertificateModule.createOne(request, createParams);
      reply.status(HttpStatus.CREATED).send(result);
    },
  );
  fastify.patch(
    '/backend/api/v1/career/profile-certificate/:id',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: paramsData, error: paramsError } = RouteParamsSchema.safeParse(request.params);
      if (paramsError) {
        throw new AppError('PFCRT009', paramsError);
      }
      const { error: bodyError, data: bodyData } = ProfileCertificatePatchBodySchema.safeParse(request.body);
      if (bodyError) {
        throw new AppError('PFCRT007', { error: bodyError });
      }
      const updateParams: ProfileCertificateUpdateOneParamsI = bodyData;
      const result = await Career.CertificateModule.updateOne(updateParams, paramsData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default profileCertificateRoutes;
