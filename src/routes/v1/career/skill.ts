import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Career from '@modules/career';
import {
  ProfileSkillDeleteBodySchema,
  ProfileSkillFetchForExternalClientSchema,
  ProfileSkillPostBodySchema,
} from '@schemas/career/skill';

import { FastifyInstance, FastifyReply } from 'fastify';

const profileSkillRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/career/profile-skills', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ProfileSkillFetchForExternalClientSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PFSKL006', queryError);
    }
    const result = await Career.SkillModule.fetchForExternalClient(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/career/profile-skills', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: bodyError, data: bodyData } = ProfileSkillPostBodySchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('PFSKL002', { error: bodyError });
    }
    const result = await Career.SkillModule.createManyExternal(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.delete('/backend/api/v1/career/profile-skills', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = ProfileSkillDeleteBodySchema.safeParse(
      JSON.parse(request.body as string),
    );
    if (bodyError) {
      throw new AppError('PFSKL009', bodyError);
    }
    await Career.SkillModule.deleteMany(request, bodyData);
    reply.status(HttpStatus.NO_CONTENT);
  });
};

export default profileSkillRoutes;
