import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Career from '@modules/career';
import { EquipmentCategoriesFetchManySchema } from '@schemas/career/equipmentCategory';
import { RouteParamsSchema } from '@schemas/common/common';
import { FastifyInstance, FastifyReply } from 'fastify';

const experienceEquipmentCategoryRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/career/experience/equipment-category/:id',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error, data } = RouteParamsSchema.safeParse(request.params);
      if (error) {
        throw new AppError('EXP014', error);
      }
      const result = await Career.EquipmentCategoryModule.fetchOne(data);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get(
    '/backend/api/v1/career/experience/equipment-categories',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error, data } = EquipmentCategoriesFetchManySchema.safeParse(request.query);
      if (error) {
        throw new AppError('EXP014', error);
      }
      const result = await Career.EquipmentCategoryModule.fetchMany(data);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default experienceEquipmentCategoryRoutes;
