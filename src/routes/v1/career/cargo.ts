import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Career from '@modules/career';
import { ExperienceCargosFetchManySchema } from '@schemas/career/cargo';
import { RouteParamsSchema } from '@schemas/common/common';
import { FastifyInstance, FastifyReply } from 'fastify';

const experienceCargoRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/career/experience/cargo/:id',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error, data } = RouteParamsSchema.safeParse(request.params);
      if (error) {
        throw new AppError('EXP014', error);
      }
      const result = await Career.CargoModule.fetchOne(data);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get('/backend/api/v1/career/experience/cargos', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = ExperienceCargosFetchManySchema.safeParse(request.query);
    if (error) {
      throw new AppError('EXP014', error);
    }
    const result = await Career.CargoModule.fetchMany(data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default experienceCargoRoutes;
