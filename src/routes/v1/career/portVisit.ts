import { HttpStatus } from '@consts/common/api/status';
import type { FastifyInstance, FastifyReply } from 'fastify';
import Career from '@modules/career';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import AppError from '@classes/AppError';
import { PortVisitFetchSchema } from '@schemas/port/portVisit';

const portVisitRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/career/port-visits', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = PortVisitFetchSchema.safeParse(request.query);
    if (error) {
      throw new AppError('PPL001');
    }
    const result = await Career.PortVisitModule.fetch(data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default portVisitRoutes;
