import { HttpStatus } from '@consts/common/api/status';
import type { FastifyInstance, FastifyReply } from 'fastify';
import { CommentCreateOneSchema, CommentFetchManySchema, CommentFetchRepliesSchema } from '@schemas/feed/comment';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import AppError from '@classes/AppError';
import FeedModule from '@modules/feed';
import { RouteParamsSchema } from '@schemas/common/common';
import { toAny } from '@utils/data/object';

const commentRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/feed/comment', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = CommentCreateOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('PCMT009');
    }
    const result = await FeedModule.CommentModule.createOne(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });

  fastify.get('/backend/api/v1/feed/comments', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = CommentFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PCMT012', queryError);
    }
    const result = await FeedModule.CommentModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/feed/comment/:id/replies', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: routeParamsData, error: routeParamsError } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('POST021', routeParamsError);
    }
    const { error: queryError, data: queryData } = CommentFetchRepliesSchema.safeParse({
      parentCommentId: routeParamsData.id,
      ...toAny(request.query),
    });
    if (queryError) {
      throw new AppError('PCMT009', queryError);
    }
    const result = await FeedModule.CommentModule.fetchReplies(request, {
      parentCommentId: routeParamsData.id,
      ...queryData,
    });
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete('/backend/api/v1/feed/comment/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: routeParamsData, error: routeParamsError } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('POST021', routeParamsError);
    }
    await FeedModule.CommentModule.deleteOne(request, { id: routeParamsData.id });
    reply.status(HttpStatus.NO_CONTENT);
  });
};

export default commentRoutes;
