import { HttpStatus } from '@consts/common/api/status';
import { FastifyInstance, FastifyReply } from 'fastify';
import {
  PostFetchManyQuerySchema,
  PostFetchManySchema,
  PostCreateOneSchema,
  PostUpdateOneSchema,
  ProfilePostFetchManyQuerySchema,
  PostSearchSchema,
} from '@schemas/feed/post';
import type { PostFetchManyI } from '@schemas/feed/post';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import AppError from '@classes/AppError';
import FeedModule from '@modules/feed';
import { RouteParamsSchema } from '@schemas/common/common';

const postRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/feed/post', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = PostCreateOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('POST008', bodyError);
    }
    const result = await FeedModule.PostModule.createOne(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });

  fastify.get('/backend/api/v1/feed/post/global-search', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = PostSearchSchema.safeParse(request.query);
    if (bodyError) {
      throw new AppError('POST008', bodyError);
    }
    const result = await FeedModule.PostModule.fetchManySearchGlobal(request, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.patch('/backend/api/v1/feed/post/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: routeParamsData, error: routeParamsError } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('POST021', routeParamsError);
    }
    const rawData = {
      postId: routeParamsData.id,
      ...(typeof request.body === 'object' && request.body !== null ? request.body : {}),
    };
    const { data: bodyData, error: bodyError } = PostUpdateOneSchema.safeParse(rawData);
    if (bodyError) {
      throw new AppError('POST008', bodyError);
    }
    const result = await FeedModule.PostModule.updateOne(request, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/feed/posts', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = PostFetchManyQuerySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('POST009', queryError);
    }

    const { error, data } = PostFetchManySchema.safeParse({
      profileId: request.profileId,
      pagination: {
        cursorId: queryData?.cursorId,
        pageSize: queryData.pageSize,
      },
      otherCursorId: queryData?.otherCursorId,
    } as PostFetchManyI);

    if (error) {
      throw new AppError('POST020', { error: error });
    }
    const result = await FeedModule.PostModule.fetchMany(data);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/feed/profile/posts', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ProfilePostFetchManyQuerySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('POST009', queryError);
    }
    const { error, data } = PostFetchManySchema.safeParse({
      profileId: queryData.profileId,
      pagination: {
        cursorId: queryData?.cursorId,
        pageSize: queryData.pageSize,
      },
    } as PostFetchManyI);
    if (error) {
      throw new AppError('POST020', { error: error });
    }
    const result = await FeedModule.PostModule.fetchProfilePosts(data);
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.get('/backend/api/v1/feed/post/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: routeParamsData, error: routeParamsError } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('POST021', routeParamsError);
    }
    const result = await FeedModule.PostModule.fetchOne(request, { id: routeParamsData.id });
    reply.status(HttpStatus.OK).send(result);
  });

  fastify.delete('/backend/api/v1/feed/post/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: routeParamsData, error: routeParamsError } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('POST021', routeParamsError);
    }
    await FeedModule.PostModule.deleteOne(request, routeParamsData);
    reply.status(HttpStatus.NO_CONTENT);
  });
};

export default postRoutes;
