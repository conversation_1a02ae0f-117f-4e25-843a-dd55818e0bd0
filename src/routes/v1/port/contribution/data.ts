import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Port from '@modules/port';
import { PortContributionUpsertOneParamsSchema } from '@schemas/port/contribution';
import { PortUnLocodeTypeSchema } from '@schemas/port/port';
import { FastifyInstance, FastifyReply } from 'fastify';

const contributionDataRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/port/contribution/data', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = PortUnLocodeTypeSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PRTCNB005', queryError);
    }
    const result = await Port.PortContributionModule.fetch(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/port/contribution/data', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = PortContributionUpsertOneParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('PRTCNB007');
    }
    const result = await Port.PortContributionModule.upsertOne(request, data);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default contributionDataRoutes;
