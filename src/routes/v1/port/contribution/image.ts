import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { SavedMultipartFile } from '@fastify/multipart';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Port from '@modules/port';
import { ContributionImageFileI, ContributionMultipleImageFileSchema } from '@schemas/common/contribution';

import { PortImageFetchForClientParamsSchema } from '@schemas/port/contribution';
import { PortUnLocodeTypeSchema } from '@schemas/port/port';

import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply } from 'fastify';

const contributionImageRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/port/contribution/image', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = PortImageFetchForClientParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PRTCNB005', queryError);
    }
    const result = await Port.PortImageContributionModule.fetchForClient(
      queryData,
      pick(queryData, ['page', 'pageSize']),
    );
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/port/contribution/image', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const files: SavedMultipartFile[] = await request.saveRequestFiles();
    if (!files?.length) {
      throw new AppError('PRTCNB009');
    }
    const { error: filesError } = ContributionMultipleImageFileSchema.safeParse(
      files.map(
        (file) =>
          ({
            mimetype: file.mimetype,
            size: file.file.bytesRead,
          }) as ContributionImageFileI,
      ),
    );
    if (filesError) {
      throw new AppError('PRTCNB008', { error: filesError });
    }
    const { error: bodyError, data: bodyData } = PortUnLocodeTypeSchema.safeParse(request.body);

    if (bodyError) {
      throw new AppError('PRTCNB007');
    }
    const result = await Port.PortImageContributionModule.uploadImagesBulk(request, {
      unLocode: bodyData.unLocode,
      dataType: bodyData.dataType,
      files,
    });
    reply.status(HttpStatus.OK).send(result);
  });
};

export default contributionImageRoutes;
