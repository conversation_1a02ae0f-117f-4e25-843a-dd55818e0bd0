import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import Port from '@modules/port';
import {
  PortVisitorCreateOneParamsSchema,
  PortVisitorDeleteOneParamsSchema,
  PortVisitorFetchForClientParamsSchema,
} from '@schemas/port/visitor';
import type { FastifyInstance, FastifyReply } from 'fastify';
const visitorRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/port/scrap-book/visitors', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = PortVisitorFetchForClientParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PRTVSR004', queryError);
    }
    const result = await Port.PortVisitorModule.fetchForClient(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/port/scrap-book/visitor', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = PortVisitorCreateOneParamsSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('PRTVSR006', { error: bodyError.errors });
    }
    const result = await Port.PortVisitorModule.createOne(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.delete(
    '/backend/api/v1/port/scrap-book/visitor',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = PortVisitorDeleteOneParamsSchema.safeParse(
        JSON.parse(request.body as string),
      );
      if (queryError) {
        throw new AppError('PRTVSR004', queryError);
      }
      await Port.PortVisitorModule.deleteOne(request, queryData);
      reply.status(HttpStatus.NO_CONTENT);
    },
  );
};

export default visitorRoutes;
