import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import Port from '@modules/port';
import { RouteParamsSchema } from '@schemas/common/common';
import {
  ScrapBookCommentCreateOneSchema,
  ScrapBookCommentFetchManySchema,
  ScrapBookCommentFetchRepliesSchema,
  ScrapBookCommentIdSchema,
} from '@schemas/port/comment';
import { toAny } from '@utils/data/object';
import type { FastifyInstance, FastifyReply } from 'fastify';

const scrapBookCommentRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/port/scrap-book/comments', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ScrapBookCommentFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('SCBKCMT005', queryError);
    }
    const result = await Port.ScrapBookCommentModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get(
    '/backend/api/v1/port/scrap-book/comment/:id/replies',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: routeParamsData, error: routeParamsError } = RouteParamsSchema.safeParse(request.params);
      if (routeParamsError) {
        throw new AppError('SCBKCMT005', routeParamsError);
      }
      const { data: queryData, error: queryError } = ScrapBookCommentFetchRepliesSchema.safeParse({
        parentCommentId: routeParamsData.id,
        ...toAny(request.query),
      });
      if (queryError) {
        throw new AppError('SCBKCMT008', { error: queryError });
      }
      const result = await Port.ScrapBookCommentModule.fetchReplies(request, queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post('/backend/api/v1/port/scrap-book/comment', async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = ScrapBookCommentCreateOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('SCBKCMT008', { error: bodyError });
    }
    const result = await Port.ScrapBookCommentModule.createOne(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.delete(
    '/backend/api/v1/port/scrap-book/comment',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: bodyData, error: bodyError } = ScrapBookCommentIdSchema.safeParse(
        JSON.parse(request.body as string),
      );
      if (bodyError) {
        throw new AppError('SCBKCMT008', bodyError);
      }
      await Port.ScrapBookCommentModule.deleteOne(request, { id: bodyData.id });
      reply.status(HttpStatus.NO_CONTENT).send();
    },
  );
};

export default scrapBookCommentRoutes;
