import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import DocumentModule from '@modules/document';
import { ProfileIdPaginationSchema, RouteParamsSchema } from '@schemas/common/common';
import type { RouteParamsI } from '@schemas/common/common';
import { IdentityPatchBodySchema, IdentityPostBodySchema } from '@schemas/document/identity';
import type { FastifyInstance, FastifyReply } from 'fastify';

const identityRoutes = (fastify: FastifyInstance): void => {
  fastify.patch('/backend/api/v1/document/identity/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: routeParamsError, data: routeParamsData } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('IDTY009', { error: routeParamsError });
    }
    const { error: bodyError, data: bodyData } = IdentityPatchBodySchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('IDTY005', { error: bodyError });
    }
    const result = await DocumentModule.IdentityModule.updateOne(
      {
        ...bodyData,
      },
      { id: routeParamsData.id },
    );
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete('/backend/api/v1/document/identity/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: paramsData, error: paramsError } = RouteParamsSchema.safeParse(request.params);
    if (paramsError) {
      throw new AppError('IDTY011', paramsError);
    }
    await DocumentModule.IdentityModule.deleteOne(request, paramsData as RouteParamsI);
    reply.status(HttpStatus.NO_CONTENT);
  });
  fastify.post('/backend/api/v1/document/identity', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: bodyError, data: bodyData } = IdentityPostBodySchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('IDTY005', { error: bodyError });
    }
    const result = await DocumentModule.IdentityModule.createOne(request, {
      ...bodyData,
    });
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.get('/backend/api/v1/document/identities', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ProfileIdPaginationSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('IDTY007', queryError);
    }
    const result = await DocumentModule.IdentityModule.fetchForExternalClient(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/document/identity/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: routeParamsError, data: routeParamsData } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('IDTY009', { error: routeParamsError });
    }
    const result = await DocumentModule.IdentityModule.fetchOneForInternalClient({
      profileId: request.profileId,
      id: routeParamsData.id,
    });
    reply.status(HttpStatus.OK).send(result);
  });
};

export default identityRoutes;
