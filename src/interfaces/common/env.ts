export type EnvVariablesI = {
  API_KEY: string;
  BACKEND_PORT: number;
  DO_ACCESS_KEY_ID: string;
  DO_REGION: string;
  DO_SECRET_KEY: string;
  DO_SIGNED_URL_EXPIRY_S: number;
  DO_SPACES: string;
  DO_SPACES_CDN_ENDPOINT: string;
  DO_SPACES_ENDPOINT: string;
  ENCRYPTION_SECRET_KEY: string;
  KAFKA_BACKEND_CLIENT_ID: string;
  KAFKA_BACKEND_BROKER: string;
  MONGO_DATABASE_URL: string;
  NODE_ENV: 'development' | 'production' | 'test';
  POSTGRES_DATABASE_URL: string;
};
