import { DBDataTypeI } from '@consts/common/data';
import { StringNullI } from '@interfaces/common/data';
import type { Country, Ship, ShipName, MainVesselType, SubVesselType } from '@prisma/postgres';
import { SubVesselTypeNestedClientI } from './subVesselType';

export type ShipNestedClientI = Pick<Ship, 'imo' | 'name'> & {
  dataType: DBDataTypeI;
};
export type FetchByImoDetailedShipClientI = Pick<Ship, 'imo' | 'mmsi' | 'callSign' | 'name' | 'yearBuilt'> & {
  imageUrl?: string;
  mainVesselType: Pick<MainVesselType, 'name'>;
  subVesselType:Pick<SubVesselType,'name'>;
  country: Pick<Country, 'name'>;
  shipNames?: Pick<ShipName, 'name' | 'fromDate' | 'toDate'>[];
  dataType: DBDataTypeI;
};

export type FetchShipClientI = {
  imo: string; // The IMO number of the ship
  currentName: string; // The current name from either Ship or ShipRawData
  matchingName: StringNullI; // The matching name (only for 'master' dataType)
  dataType: 'master' | 'rawData'; // Indicates the source table
};

export type FetchPrePopulateResultI = Pick<Ship, 'imo' | 'name' | 'length' | 'beam' | 'gt' | 'dwt'> & {
  dataType: DBDataTypeI;
  subVesselType: SubVesselTypeNestedClientI
};
