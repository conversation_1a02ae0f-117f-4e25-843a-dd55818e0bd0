import { DBDataTypeI } from '@consts/common/data';
import type { EquipmentCategory } from '@prisma/postgres';
import { UUIDI } from '@schemas/common/common';
export type EquipmentCategoryClientI = Pick<EquipmentCategory, 'id' | 'name' | 'hasFuelType'> & {
  dataType: DBDataTypeI;
};

export type EquipmentCategoryNestedClientI = Pick<EquipmentCategory, 'id' | 'name'> & {
  dataType: DBDataTypeI;
};

export type EquipmentCategoryShipNestedClientI = {
  id: UUIDI;
  category: EquipmentCategoryNestedClientI;
  manufacturerName: string;
  model: string;
};
