import { Profile, ScrapBookComment } from '@prisma/postgres';

type ProfileForComment = Pick<Profile, 'id' | 'name' | 'avatar'> & {
  designation: {
    id: string;
    name: string;
    dataType: 'master' | 'raw';
  } | null;
  entity: {
    id: string;
    name: string;
    dataType: 'master' | 'raw';
  } | null;
};

export type ScrapBookCommentFetchForClientI = Pick<ScrapBookComment, 'id' | 'text' | 'createdAt' | 'updatedAt'> & {
  cursorId: number;
  Profile: ProfileForComment;
  replies: ScrapBookCommentFetchForClientI[];
};

export type ScrapBookCommentFetchManyResultI = {
  comments: ScrapBookCommentFetchForClientI[];
  total: number;
  cursorId: number | null;
};

export type ScrapBookCommentCreateOneResultI = Pick<ScrapBookComment, 'id'> & {
  cursorId: number;
};

export type ProfileItemI = Pick<
  Profile,
  | 'id'
  | 'name'
  | 'avatar'
  | 'designationText'
  | 'entityText'
  | 'designationAlternativeId'
  | 'designationRawDataId'
  | 'entityId'
  | 'entityRawDataId'
>;
export type ScrapBookCommentTempResultItemI = Pick<
  ScrapBookComment,
  'id' | 'text' | 'createdAt' | 'updatedAt' | 'cursorId'
> & {
  Profile: ProfileItemI;
};
