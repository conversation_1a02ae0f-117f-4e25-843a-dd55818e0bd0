import type { DateNullI, NumberNullI, StringNullI, UndefinedNullableI } from '@interfaces/common/data';
import { DepartmentClientI } from '@interfaces/company/department';
import { SkillNestedClientI } from '@interfaces/company/skill';
import { EquipmentCategoryShipNestedClientI } from '@interfaces/ship/equipmentCategory';
import { SubVesselTypeNestedClientI } from '@interfaces/ship/subVesselType';

export type ExperienceShipResultTempI = {
  id: string;
  shipImo: StringNullI;
  shipRawDataImo: StringNullI;
  name: StringNullI;
  sizeGt: NumberNullI;
  dwt: NumberNullI;
  powerKw: NumberNullI;
  details: StringNullI;
  fromDate: DateNullI;
  toDate: DateNullI;
  subVesselTypeId: StringNullI;
  subVesselTypeRawDataId: StringNullI;
  subVesselTypeName: StringNullI;
  subVesselTypeRawDataName: StringNullI;
  departmentAlternativeId: StringNullI;
  departmentRawDataId: StringNullI;
  departmentAlternativeName: StringNullI;
  departmentRawDataName: StringNullI;
  skills:
    | {
        skillId: StringNullI;
        skillRawDataId: StringNullI;
        skillName: StringNullI;
        skillRawDataName: StringNullI;
        skillCategory: StringNullI;
        skillRawDataCategory: StringNullI;
      }[]
    | null;
  equipments:
    | {
        equipmentId: StringNullI;
        equipmentRawDataId: StringNullI;
        equipmentName: StringNullI;
        equipmentRawDataName: StringNullI;
        id: StringNullI;
        manufacturerName: StringNullI;
        model: StringNullI;
      }[]
    | null;
};

export type ExperienceShipResultI = Pick<ExperienceShipResultTempI, 'id'> &
  Partial<
    Pick<ExperienceShipResultTempI, 'name' | 'sizeGt' | 'powerKw' | 'fromDate' | 'toDate' | 'dwt' | 'details'>
  > & {
    subVesselType?: SubVesselTypeNestedClientI;
    department?: DepartmentClientI;
    skills?: UndefinedNullableI<SkillNestedClientI[]>;
    equipments?: UndefinedNullableI<EquipmentCategoryShipNestedClientI[]>;
  };
