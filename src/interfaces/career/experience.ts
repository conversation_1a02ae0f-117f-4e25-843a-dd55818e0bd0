import { StringNullI, DateNullI, StringUndefinedNullI, TotalDataI, UndefinedNullableI } from '@interfaces/common/data';
import { DesignationNestedClientI } from '@interfaces/company/designation';
import { EntityNestedClientI } from '@interfaces/company/entity';
import { ShipNestedClientI } from '@interfaces/ship/ship';
import type { Experience, ExperienceShip } from '@prisma/postgres';
import { PortVisitorFetchResultI } from './portVisit';
import { MainVesselTypeNestedClientI } from '@interfaces/ship/mainVesselType';
import { SubVesselTypeNestedClientI } from '@interfaces/ship/subVesselType';
export type ExperienceFetchForClientRawResultI = {
  id: string; // UUID
  entityId: StringNullI; // UUID (nullable due to LEFT JOIN)
  entityName: StringNullI; // nullable due to LEFT JOIN
  entityRawDataId: StringNullI; // UUID (nullable due to LEFT JOIN)
  entityRawDataName: StringNullI; // nullable due to LEFT JOIN
  years: number;
  months: number;
  designations:
    | {
        experienceDesignationId?: StringUndefinedNullI;
        fromDate: DateNullI;
        toDate: DateNullI;
        designationAlternativeId: StringNullI; // UUID
        designationRawDataId: StringNullI; // UUID
        designationName: StringNullI;
        designationRawDataName: StringNullI;
        ships:
          | {
              id: string;
              fromDate: DateNullI;
              toDate: DateNullI;
              shipImo: StringNullI;
              shipRawDataImo: StringNullI;
              name: StringNullI;
              subVesselTypeId: StringNullI; // UUID
              subVesselTypeRawDataId: StringNullI; // UUID
              subVesselTypeName: StringNullI;
              subVesselTypeRawDataName: StringNullI;
            }[]
          | null;
      }[]
    | null;
};

export type ExperienceShipClientI = {
  id: string;
  name: StringNullI;
  ship: ShipNestedClientI;
  subVesselType: SubVesselTypeNestedClientI;
  fromDate: DateNullI;
  toDate: DateNullI;
};
export type ExperienceDesignationClientI = {
  experienceDesignationId?: StringUndefinedNullI;
  designation: DesignationNestedClientI;
  fromDate: DateNullI;
  toDate: DateNullI;
  ships: ExperienceShipClientI[];
};
export type ExperienceFetchForClientDataResultI = Pick<Experience, 'id' | 'years' | 'months'> & {
  entity: EntityNestedClientI;
  designations: ExperienceDesignationClientI[];
};
export type ExperienceFetchForClientResultI = TotalDataI<ExperienceFetchForClientDataResultI> & {
  portVisits?: UndefinedNullableI<TotalDataI<PortVisitorFetchResultI>>;
};
export type ExperienceFetchOneForExternalClientResultI = {
  entity: EntityNestedClientI;
  designations: ExperienceDesignationClientI[];
};
export type ExperienceShipNestedClientI = ShipNestedClientI &
  Pick<ExperienceShip, 'fromDate' | 'toDate'> & {
    mainVesselType: MainVesselTypeNestedClientI;
  };
