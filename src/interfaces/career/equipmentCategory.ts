import { BooleanNullI, DateNullI, NullableI, StringNullI } from '@interfaces/common/data';
import { EquipmentCategoryClientI, EquipmentCategoryNestedClientI } from '@interfaces/ship/equipmentCategory';
import { FuelTypeNestedClientI } from '@interfaces/ship/fuelType';

export type ExperienceEquipmentCategorySQLI = {
  id: string;
  equipmentCategoryId: StringNullI;
  equipmentCategoryRawDataId: StringNullI;
  manufacturerName: StringNullI;
  model: StringNullI;
  powerCapacity: StringNullI;
  details: StringNullI;
  fromDate: DateNullI;
  toDate: DateNullI;
  equipmentCategoryName: StringNullI;
  equipmentCategoryHasFuelType: BooleanNullI;
  equipmentCategoryRawDataName: StringNullI;
  equipmentCategoryRawDataHasFuelType: BooleanNullI;
  fuelTypes:
    | {
        fuelTypeId: StringNullI;
        fuelTypeRawDataId: StringNullI;
        fuelTypeName: StringNullI;
        fuelTypeRawDataName: StringNullI;
      }[]
    | null;
};
export type ExperienceEquipmentCategoriesSQLI = {
  id: string;
  equipmentCategoryId: StringNullI;
  equipmentCategoryRawDataId: StringNullI;
  equipmentCategoryName: StringNullI;
  equipmentCategoryRawDataName: StringNullI;
};
export type ExperienceEquipmentCategoryResultI = {
  id: string;
  equipmentCategory: EquipmentCategoryClientI;
  manufacturerName: StringNullI;
  model: StringNullI;
  powerCapacity: StringNullI;
  details: StringNullI;
  fuelTypes: NullableI<FuelTypeNestedClientI[]>;
};

export type ExperienceEquipmentCategoriesResultI = {
  id: string;
  equipmentCategory: EquipmentCategoryNestedClientI;
};
