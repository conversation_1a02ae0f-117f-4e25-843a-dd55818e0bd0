import { DBDataTypeI } from '@consts/common/data';
import { Skill, Prisma } from '@prisma/postgres';
import { EntityFilterI } from './entity';
import { CertificateCourseNestedClientI } from './certificateCourse';
import { DegreeNestedClientI } from './degree';
import { IdTypeI } from '@schemas/common/common';

export type SkillClientI = Skill & {
  dataType: DBDataTypeI;
};
export type SkillNestedClientI = Pick<Skill, 'id' | 'name' | 'category'> & {
  dataType: DBDataTypeI;
};
export type SkillModuleFetchsertParamsI = Pick<Prisma.SkillRawDataCreateInput, 'name' | 'category'>;

export type FetchForEntityCertificateCourseFilterI = {
  profileId: string;
  entity: EntityFilterI;
  certificateCourse: CertificateCourseNestedClientI;
};

export type FetchSpecificForEntityCertificateCourseFilterI = FetchForEntityCertificateCourseFilterI & {
  idTypes: IdTypeI[];
};

export type DeleteManyForEntityCertificateCourseFitlerI = {
  profileId: string;
  entity: EntityFilterI;
  certificateCourse: CertificateCourseNestedClientI;
  idTypes: IdTypeI[];
};
export type FetchForEntityDegreeFilterI = {
  profileId: string;
  entity: EntityFilterI;
  degree: DegreeNestedClientI;
};

export type FetchSpecificForEntityDegreeFilterI = FetchForEntityDegreeFilterI & {
  idTypes: IdTypeI[];
};
export type DeleteManyForEntityDegreeFitlerI = {
  profileId: string;
  entity: EntityFilterI;
  degree: DegreeNestedClientI;
  idTypes: IdTypeI[];
};

export type FetchForExperienceShipFilterI = {
  profileId: string;
  experienceShipId: string;
};
