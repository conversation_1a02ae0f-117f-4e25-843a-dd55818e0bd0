import { ProfileDataI, ProfileExternalI } from '@interfaces/user/profile';
import type { Post, PostMedia } from '@prisma/postgres';

export type PostDataClientI = {
  caption: string;
  id: string;
  cursorId?: number;
  reactionsCount: number;
  totalCommentsCount: number;
  isLiked: boolean;
  Profile: ProfileDataI;
  Media: Pick<PostMedia, 'caption' | 'fileUrl'>[];
};
export type PostExternalClientI = {
  caption: string;
  id: string;
  cursorId?: number;
  reactionsCount: number;
  totalCommentsCount: number;
  isLiked: boolean;
  Profile: ProfileExternalI;
  Media: Pick<PostMedia, 'caption' | 'fileUrl'>[];
};
export type PostFetchManyResultI = { posts: PostExternalClientI[]; cursorId?: number; otherCursorId?: number };

export type PostForNotificationI = Pick<Post, 'id' | 'caption'> & { image?: string };
