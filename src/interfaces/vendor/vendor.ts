import { ObjUnknownI } from '@interfaces/common/data';
import Firebase, { type FirebaseConfigI } from '@navicater/vendor-firebase';
import Google, { type GoogleConfigI } from '@navicater/vendor-google';
import type { Vendor, VendorNamesE } from '@prisma/mongodb';
export type VendorI = Pick<Vendor, 'name' | 'type'> & {
  config: ObjUnknownI;
};
export type GetVendorResultI =
  | ({ name: VendorNamesE } & { config: FirebaseConfigI; instance: Firebase })
  | { config: GoogleConfigI; instance: Google };
