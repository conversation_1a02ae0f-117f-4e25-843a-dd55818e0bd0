import { DocumentAllowedMimeTypeE, FileTypeE } from '@consts/storage/storage';
import { formatBytes } from '@utils/data/file';
import { z } from 'zod';

export const DocumentFileSchema = z
  .object({
    mimetype: DocumentAllowedMimeTypeE,
    size: z.number().min(0).max(FileTypeE.pdf.maxSize),
  })
  .superRefine((data, ctx) => {
    if (data.mimetype === FileTypeE.pdf.mime) {
      if (data.size > FileTypeE.pdf.maxSize) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `File size can't be greater than ${formatBytes(FileTypeE.pdf.maxSize)}`,
        });
      }
    } else if (data.mimetype === FileTypeE.webp.mime) {
      if (data.size > FileTypeE.webp.maxSize) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `File size can't be greater than ${formatBytes(FileTypeE.webp.maxSize)}`,
        });
      }
    } else {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "File type isn't supported",
        path: ['file'],
      });
    }
  });

export type DocumentFileI = z.infer<typeof DocumentFileSchema>;
