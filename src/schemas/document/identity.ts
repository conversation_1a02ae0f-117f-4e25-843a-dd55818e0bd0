import { IdentityTypeE } from '@consts/document/identity';
import { CountryIso2Schema } from '@schemas/common/common';
import { z } from 'zod';

export const IdentityPostBodySchema = z
  .object({
    documentNo: z.string().min(3).max(50),
    fileUrl: z.string().url(),
    type: IdentityTypeE,
    countryIso2: CountryIso2Schema,
    fromDate: z.coerce.date(),
    untilDate: z.coerce.date(),
  })
  .refine((data) => data.fromDate <= data.untilDate, {
    message: "from date can't be greater until date",
  });

export type IdentityPostBodyI = z.infer<typeof IdentityPostBodySchema>;

export const IdentityPatchBodySchema = z
  .object({
    documentNo: z.string().min(3).max(50).optional(),
    fileUrl: z.string().url().optional(),
    countryIso2: CountryIso2Schema.optional(),
    fromDate: z.coerce.date().optional(),
    untilDate: z.coerce.date().optional(),
  })
  .superRefine((data, ctx) => {
    if (!Object.keys(data).some((key) => data[key])) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'At least one attribute is required',
      });
    } else if (data?.fromDate && data?.untilDate && data.fromDate > data.untilDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "From date can't be greater than until date",
        path: ['fromDate'],
      });
    }
  });

export type IdentityPatchBodyI = z.infer<typeof IdentityPostBodySchema>;
