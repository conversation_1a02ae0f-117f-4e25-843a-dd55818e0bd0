import { DBDataTypeE } from '@consts/common/data';
import { DepartmentNameR } from '@consts/common/regex/regex';
import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const DepartmentOptionsFetchSchema = PaginationSchema.extend({
  search: z.string().max(60).optional(),
});
export type DepartmentOptionsFetchI = z.infer<typeof DepartmentOptionsFetchSchema>;

export const DepartmentNameSchema = z.object({
  name: z.string().min(2).max(100).regex(DepartmentNameR),
});
export type DepartmentNameI = z.infer<typeof DepartmentNameSchema>;
export const DepartmentIdClientSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});
export type DepartmentIdClientI = z.infer<typeof DepartmentIdClientSchema>;
