import { SkillNameR } from '@consts/common/regex/regex';
import { z } from 'zod';
import { PaginationSchema } from '../common/common';
import { SkillCategoryE } from '@consts/company/skill';

export const SkillOptionsFetchSchema = PaginationSchema.extend({
  search: z.string().min(1).max(100).regex(SkillNameR),
});
export type SkillModuleFetchParamsI = z.infer<typeof SkillOptionsFetchSchema>;
export const SkillNameSchema = z.object({
  name: z.string().min(2).max(100).regex(SkillNameR),
  category: SkillCategoryE,
});
export type SkillNameI = z.infer<typeof SkillNameSchema>;
