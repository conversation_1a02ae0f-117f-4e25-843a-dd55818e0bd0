import { DegreeNameR } from '@consts/common/regex/regex';
import { z } from 'zod';
import { PaginationSchema } from '../common/common';

export const DegreeOptionsFetchSchema = PaginationSchema.extend({
  search: z.string().min(1).max(150).regex(DegreeNameR),
});
export type DegreeModuleFetchParamsI = z.infer<typeof DegreeOptionsFetchSchema>;

export const DegreeNameSchema = z.object({
  name: z.string().min(2).max(150).regex(DegreeNameR),
});
export type DegreeNameI = z.infer<typeof DegreeNameSchema>;
