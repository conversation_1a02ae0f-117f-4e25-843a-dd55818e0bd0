import { DBDataTypeE } from '@consts/common/data';
import { DesignationNameR } from '@consts/common/regex/regex';
import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const DesignationOptionsFetchSchema = PaginationSchema.extend({
  search: z.string().min(1).max(100).regex(DesignationNameR),
});
export type DesignationOptionsFetchI = z.infer<typeof DesignationOptionsFetchSchema>;

export const DesignationNameSchema = z.object({
  name: z.string().min(2).max(100).regex(DesignationNameR),
});
export type DesignationNameI = z.infer<typeof DesignationNameSchema>;
export const DesignationIdClientSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});
export type DesignationIdClientI = z.infer<typeof DesignationIdClientSchema>;
