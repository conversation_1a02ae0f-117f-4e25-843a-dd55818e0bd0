import { FileTypeE, ImageAllowedMimeTypeE, MAX_NO_OF_FILES } from '@consts/storage/storage';
import { formatBytes } from '@utils/data/file';
import { z } from 'zod';
import { IdTypeSchema } from './common';
import { ShipImoClientSchema } from '@schemas/ship/ship';
import { PortUnLocodeTypeSchema } from '@schemas/port/port';
export const ContributionValueSchema = z.union([
  z.string().min(1).max(100),
  z.number(),
  z.number().int(),
  IdTypeSchema,
  ShipImoClientSchema,
  PortUnLocodeTypeSchema,
]);
export type ContributionValueI = z.infer<typeof ContributionValueSchema>;
export const ContributionParamsSchema = z.object({
  label: z.string().min(2).max(100),
  value: ContributionValueSchema,
});
export type ContributionParamsI = z.infer<typeof ContributionParamsSchema>;

export const ContributionImageFileSchema = z
  .object({
    mimetype: ImageAllowedMimeTypeE,
    size: z.number().min(0).max(FileTypeE.webp.maxSize),
  })
  .superRefine((data, ctx) => {
    if (data.mimetype === FileTypeE.pdf.mime) {
      if (data.size > FileTypeE.pdf.maxSize) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `File size can't be greater than ${formatBytes(FileTypeE.webp.maxSize)}`,
        });
      }
    } else if (data.mimetype === FileTypeE.webp.mime) {
      if (data.size > FileTypeE.webp.maxSize) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `File size can't be greater than ${formatBytes(FileTypeE.webp.maxSize)}`,
        });
      }
    } else {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "File type isn't supported",
        path: ['file'],
      });
    }
  });

export type ContributionImageFileI = z.infer<typeof ContributionImageFileSchema>;
export const ContributionMultipleImageFileSchema = z
  .array(ContributionImageFileSchema)
  .max(MAX_NO_OF_FILES, 'Maximum 8 images are allowed');
export type ContributionMultipleImageFileI = z.infer<typeof ContributionMultipleImageFileSchema>;
