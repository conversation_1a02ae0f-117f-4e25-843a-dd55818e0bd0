import { CountryIso2Schema, IdTypeSchema, PaginationSchema } from '@schemas/common/common';
import { z } from 'zod';

export const CityFetchOneParamsSchema = z.object({
  city: IdTypeSchema,
  countryIso2: CountryIso2Schema.optional(),
});
export type CityFetchOneParamsI = z.infer<typeof CityFetchOneParamsSchema>;

export const CityFetchForClientParamsSchema = z.object({
  search: z.string().min(1).optional(),
  countryIso2: CountryIso2Schema.optional(),
});
export type CityFetchForClientParamsI = z.infer<typeof CityFetchForClientParamsSchema>;

export const CityFetchForClientParamsPaginationSchema = CityFetchForClientParamsSchema.merge(PaginationSchema);
export const CityFetchsertParamsSchema = z.object({
  name: z.string().min(2).optional(),
  countryIso2: CountryIso2Schema.optional(),
  geoNameId: z.string().optional(),
});
export type CityFetchsertParamsI = z.infer<typeof CityFetchsertParamsSchema>;
