import { ReactionTypeE } from '@consts/feed/reaction';
import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const ReactionUpsertSchema = z.object({
  postId: UUIDSchema,
  reactionType: ReactionTypeE,
});
export type ReactionUpsertI = z.infer<typeof ReactionUpsertSchema>;

export const ReactionPostIdSchema = z.object({
  postId: UUIDSchema,
});
export type ReactionPostIdI = z.infer<typeof ReactionPostIdSchema>;

export const ReactionFetchManySchema = ReactionPostIdSchema.merge(PaginationSchema);
export type ReactionFetchManyI = z.infer<typeof ReactionFetchManySchema>;
