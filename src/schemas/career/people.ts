import { PaginationSchema } from '@schemas/common/common';
import { PortUnLocodeTypeSchema } from '@schemas/port/port';
import { ShipImoClientSchema } from '@schemas/ship/ship';
import { z } from 'zod';

export const PeopleFetchPeopleOnShipSchema = PaginationSchema.merge(ShipImoClientSchema);
export type PeopleFetchPeopleOnShipI = z.infer<typeof PeopleFetchPeopleOnShipSchema>;

export const PeopleFetchPeopleOnPortSchema = PaginationSchema.merge(PortUnLocodeTypeSchema);
export type PeopleFetchPeopleOnPortI = z.infer<typeof PeopleFetchPeopleOnPortSchema>;
