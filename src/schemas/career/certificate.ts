import { CertificateCourseTypeE } from '@consts/company/certificateCourse';
import { IdTypeSchema, ProfileIdPaginationSchema } from '@schemas/common/common';
import { isEmpty } from '@utils/data/object';
import { z } from 'zod';

export const ProfileCertificatePostBodySchema = z
  .object({
    institute: IdTypeSchema,
    certificateCourse: IdTypeSchema,
    fileUrl: z.string().url().optional(),
    fromDate: z.coerce.date(),
    untilDate: z.coerce.date(),
    skills: z.array(IdTypeSchema).nullable(),
  })
  .superRefine((data, ctx) => {
    if (data.fromDate > data.untilDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "From date can't be greater than Until date",
        path: ['fromDate'],
      });
    }
  });
export type ProfileCertificatePostBodyI = z.infer<typeof ProfileCertificatePostBodySchema>;

export const ProfileCertificateFetchForClientSchema = ProfileIdPaginationSchema.merge(
  z.object({
    type: CertificateCourseTypeE,
  }),
);
export type ProfileCertificateFetchForClientI = z.infer<typeof ProfileCertificateFetchForClientSchema>;

export const ProfileCertificatePatchBodySchema = z
  .object({
    institute: IdTypeSchema.optional(),
    certificateCourse: IdTypeSchema.optional(),
    fromDate: z.coerce.date().optional(),
    untilDate: z.coerce.date().optional(),
    skillsToAdd: z.array(IdTypeSchema).nullable().optional(),
    skillsToDelete: z.array(IdTypeSchema).nullable().optional(),
  })
  .superRefine((data, ctx) => {
    if (isEmpty(data)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'At least one attribute is required',
      });
    } else if (data?.fromDate && data?.untilDate && data.fromDate > data.untilDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "From date can't be greater than Until date",
        path: ['fromDate'],
      });
    }
  });
export type ProfileCertificatePatchBodyI = z.infer<typeof ProfileCertificatePatchBodySchema>;
