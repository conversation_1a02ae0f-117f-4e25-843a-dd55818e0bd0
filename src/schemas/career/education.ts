import { IdTypeSchema } from '@schemas/common/common';
import { isEmpty } from '@utils/data/object';
import { z } from 'zod';

export const ProfileEducationCreateOneParamsSchema = z
  .object({
    institute: IdTypeSchema,
    degree: IdTypeSchema,
    fromDate: z.coerce.date(),
    toDate: z
      .string()
      .transform((data) => new Date(data))
      .nullable(),
    skills: z.array(IdTypeSchema).nullable(),
  })
  .superRefine((data, ctx) => {
    if (data?.toDate && data.fromDate > data.toDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "From date can't be greater than To date",
        path: ['fromDate'],
      });
    }
  });
export type ProfileEducationCreateOneParamsI = z.infer<typeof ProfileEducationCreateOneParamsSchema>;

export const ProfileEducationPatchBodySchema = z
  .object({
    institute: IdTypeSchema.optional(),
    degree: IdTypeSchema.optional(),
    fromDate: z.coerce.date().optional(),
    toDate: z.coerce.date().nullable().optional(),
    skillsToAdd: z.array(IdTypeSchema).nullable().optional(),
    skillsToDelete: z.array(IdTypeSchema).nullable().optional(),
  })
  .superRefine((data, ctx) => {
    if (isEmpty(data)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'At least one attribute is required',
      });
    } else if (data?.fromDate && data?.toDate && data.fromDate > data.toDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "From date can't be greater than Until date",
        path: ['fromDate'],
      });
    }
  });
export type ProfileEducationPatchBodyI = z.infer<typeof ProfileEducationPatchBodySchema>;
