import { CursorPaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

const TextSchema = z.string().min(1).max(255);

export const ScrapBookCommentFetchManySchema = CursorPaginationSchema.extend({
  scrapBookPostId: UUIDSchema,
});
export type ScrapBookCommentFetchManyI = z.infer<typeof ScrapBookCommentFetchManySchema>;

export const ScrapBookCommentFetchRepliesSchema = CursorPaginationSchema.extend({
  scrapBookPostId: UUIDSchema,
  parentCommentId: UUIDSchema,
});
export type ScrapBookCommentFetchRepliesI = z.infer<typeof ScrapBookCommentFetchRepliesSchema>;

export const ScrapBookCommentCreateOneSchema = z.object({
  scrapBookPostId: UUIDSchema,
  parentCommentId: UUIDSchema.optional(),
  text: TextSchema,
});
export type ScrapBookCommentCreateOneI = z.infer<typeof ScrapBookCommentCreateOneSchema>;
export const ScrapBookCommentIdSchema = z.object({
  id: UUIDSchema,
});
export type ScrapBookCommentIdSchemaI = z.infer<typeof ScrapBookCommentIdSchema>;
