import { DBDataTypeE } from '@consts/common/data';
import { PortR, UnLocodeR } from '@consts/common/regex/regex';
import { CountryIso2Schema } from '@schemas/common/common';
import { createDecimalPrecisionSchema } from '@utils/common/schema';
import { z } from 'zod';

export const UnLocodeSchema = z.string().length(5).regex(UnLocodeR);

export const UnLocodeTypeSchema = z.object({
  unLocode: UnLocodeSchema,
  dataType: DBDataTypeE,
});
export const PortNameSchema = z.string().min(2).max(255).regex(PortR);
export const TimezoneIso2Schema = CountryIso2Schema;
export const LatitudeSchema = createDecimalPrecisionSchema({ maxDigits: 6, decimalPlaces: 6, min: -90, max: 90 });
export const LongitudeSchema = createDecimalPrecisionSchema({ maxDigits: 7, decimalPlaces: 6, min: -180, max: 180 });
export const NoOfTerminalsSchema = z.number().int().min(0);
export const NoOfBerthsSchema = z.number().int().min(0);
export const MaxDraughtSchema = createDecimalPrecisionSchema({ maxDigits: 10, decimalPlaces: 2 });
export const MaxDeadweightSchema = createDecimalPrecisionSchema({ maxDigits: 8, decimalPlaces: 2 });
export const MaxLengthSchema = createDecimalPrecisionSchema({ maxDigits: 7, decimalPlaces: 2 });
export const MaxAirDraughtSchema = createDecimalPrecisionSchema({ maxDigits: 10, decimalPlaces: 2 });
