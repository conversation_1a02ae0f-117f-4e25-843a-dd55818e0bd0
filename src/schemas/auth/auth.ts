import { PlatformE } from '@consts/app/platform';
import { AuthTypeE } from '@consts/auth/common';
import { EmailSchema, UsernameSchema, UUIDSchema, VersionNoSchema } from '@schemas/common/common';
import { DeviceTokenSchema } from '@schemas/communication/session';
import { z } from 'zod';
export const AuthExtendedSchema = z.object({
  ip: z.string().ip(),
  versionNo: VersionNoSchema,
  deviceId: UUIDSchema,
  platform: PlatformE,
});
export const PasswordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .max(32, 'Password can be at most 32 characters long')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[@$!%*?&^#]/, 'Password must contain at least one special character (@, $, !, %, *, ?, &, ^, #)');
export const AuthLoginBodySchema = z
  .object({
    type: AuthTypeE,
    email: EmailSchema.optional(),
    password: PasswordSchema.optional(),
    googleToken: z.string().optional(),
    deviceToken: DeviceTokenSchema,
  })
  .superRefine((data, ctx) => {
    if (data.type === 'EMAIL_PASSWORD') {
      // If type is "EMAIL_PASSWORD", email and password are mandatory
      if (!data.email || !data.password) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'email and password are required when type is "EMAIL_PASSWORD"',
          path: ['email', 'password'],
        });
      }
    } else if (data.type === 'GOOGLE') {
      // If type is "GOOGLE", googleToken is mandatory
      if (!data.googleToken) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'googleToken is required when type is "GOOGLE"',
          path: ['googleToken'],
        });
      }
    } else {
      // If type is neither "EMAIL_PASSWORD" nor "GOOGLE", throw an error
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'type must be either "EMAIL_PASSWORD" or "GOOGLE"',
        path: ['type'],
      });
    }
  });
export type AuthLoginBodyI = z.infer<typeof AuthLoginBodySchema>;

export const AuthLoginParamsSchema = AuthLoginBodySchema.and(AuthExtendedSchema);

export type AuthLoginParamsI = z.infer<typeof AuthLoginParamsSchema>;
// .refine(data=>data.password===data.confirmPassword,{
//   message:'Passwords must match',path:['confirmPassword']
// });
export const AuthRegisterBodySchema = z
  .object({
    username: UsernameSchema,
    type: AuthTypeE,
    //#region GOOGLE
    googleToken: z.string().optional(),
    //#endregion GOOGLE
    //#region EMAIL_PASSWORD
    email: EmailSchema.optional(),
    password: PasswordSchema.optional(),
    confirmPassword: PasswordSchema.optional(),
    //#endregion EMAIL_PASSWORD
  })
  .superRefine((data, ctx) => {
    if (data.type === 'GOOGLE') {
      // If type is "GOOGLE", googleToken is mandatory
      if (!data.googleToken) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'googleToken is required when type is "GOOGLE"',
          path: ['googleToken'],
        });
      }
    } else if (data.type === 'EMAIL_PASSWORD') {
      // If type is "EMAIL_PASSWORD", email, password and confirmPassword are mandatory
      if (!data.email || !data.password || !data.confirmPassword) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'email, password and confirmPassword are required when type is "EMAIL_PASSWORD"',
          path: ['email', 'password', 'confirmPassword'],
        });
      } else if (data.password !== data.confirmPassword) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'password and confirmPassword must be the same',
          path: ['confirmPassword'],
        });
      }
    } else {
      // If type is neither "EMAIL_PASSWORD" nor "GOOGLE", throw an error
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'type must be either "EMAIL_PASSWORD" or "GOOGLE"',
        path: ['type'],
      });
    }
  });

export type AuthRegisterBodyI = z.infer<typeof AuthRegisterBodySchema>;

export const AuthRegisterParamsSchema = AuthRegisterBodySchema.and(AuthExtendedSchema);

export type AuthRegisterParamsI = z.infer<typeof AuthRegisterParamsSchema>;
