// import { z } from 'zod';

// const passwordSchema = z
//   .string({ required_error: 'Password is required' })
//   .min(8, 'Password must be at least 8 characters')
//   .max(100, 'Password cannot exceed 30 characters')
//   .regex(
//     /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
//     'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
//   );

// const emailSchema = z
//   .string({ required_error: 'Email is required' })
//   .email('Invalid email format')
//   .transform((email) => email.toLowerCase());

// const phoneSchema = z
//   .string({ required_error: 'Phone number is required' })
//   .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format')
//   .max(20, 'Phone number cannot exceed 20 characters');

// export const SignupSchema = z
//   .object({
//     email: emailSchema,
//     password: passwordSchema,
//     firstName: z
//       .string({ required_error: 'First name is required' })
//       .min(2, 'First name must be at least 2 characters')
//       .max(50, 'First name cannot exceed 50 characters')
//       .regex(/^[a-zA-Z\s-']+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
//     lastName: z
//       .string()
//       .min(2, 'Last name must be at least 2 characters')
//       .max(50, 'Last name cannot exceed 50 characters')
//       .regex(/^[a-zA-Z\s-']+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes')
//       .optional(),
//     phone: phoneSchema,
//   })
//   .strict();

// export type SignupSchemaType = z.infer<typeof SignupSchema>;

// export const LoginSchema = z
//   .object({
//     email: emailSchema,
//     password: z.string({ required_error: 'Password is required' }).min(1, 'Password is required'),
//   })
//   .strict();

// export type LoginSchemaType = z.infer<typeof LoginSchema>;

// export const ForgotPasswordSchema = z
//   .object({
//     email: emailSchema,
//   })
//   .strict();

// export type ForgotPasswordSchemaType = z.infer<typeof ForgotPasswordSchema>;

// export const ResetPasswordSchema = z
//   .object({
//     token: z
//       .string({ required_error: 'Reset token is required' })
//       .min(1, 'Reset token is required')
//       .max(100, 'Invalid reset token'),
//     newPassword: passwordSchema,
//   })
//   .strict();

// export type ResetPasswordSchemaType = z.infer<typeof ResetPasswordSchema>;

// export const VerifyEmailSchema = z
//   .object({
//     verificationCode: z
//       .string({ required_error: 'Verification code is required' })
//       .length(6, 'Verification code must be exactly 6 characters')
//       .regex(/^\d+$/, 'Verification code must contain only numbers'),
//   })
//   .strict();

// export type VerifyEmailSchemaType = z.infer<typeof VerifyEmailSchema>;

// export const UserResponseSchema = z.object({
//   id: z.string({ required_error: 'User ID is required' }),
//   email: z.string({ required_error: 'Email is required' }),
//   isVerified: z.boolean({ required_error: 'Verification status is required' }),
//   profile: z.object({
//     firstName: z.string({ required_error: 'First name is required' }),
//     lastName: z.string().nullable(),
//     avatar: z.string().optional(),
//   }),
// });

// export const AuthResponseSchema = z.object({
//   user: UserResponseSchema,
//   token: z.string({ required_error: 'Authentication token is required' }),
// });

// export type AuthResponseSchemaType = z.infer<typeof AuthResponseSchema>;
