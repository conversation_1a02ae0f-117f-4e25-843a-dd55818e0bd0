import { z } from 'zod';
import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { RequestStatusTypeE } from '@consts/network/request';

export const RequestUpsertOneForSenderSchema = z.object({
  receiverProfileId: UUIDSchema,
  requestedStatus: RequestStatusTypeE,
});
export type RequestUpsertOneForSenderI = z.infer<typeof RequestUpsertOneForSenderSchema>;

export const RequestUpsertOneForReceiverSchema = z.object({
  senderProfileId: UUIDSchema,
  requestedStatus: RequestStatusTypeE,
});
export type RequestUpsertOneForReceiverI = z.infer<typeof RequestUpsertOneForReceiverSchema>;

export const RequestFetchManySchema = PaginationSchema;
export type RequestFetchManyI = z.infer<typeof RequestFetchManySchema>;
