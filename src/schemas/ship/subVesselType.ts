import { DBDataTypeE } from '@consts/common/data';
import { VesselTypeR } from '@consts/common/regex/regex';
import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const SubVesselTypeFetchForClientSchema = PaginationSchema.merge(
  z.object({
    search: z
      .string()
      .min(1)
      .max(100)
      .regex(VesselTypeR)
      .transform((data) => data.trim().toLowerCase()),
  }),
);
export type SubVesselTypeFetchForClientI = z.infer<typeof SubVesselTypeFetchForClientSchema>;

export const SubVesselTypeFetchsertSchema = z.object({
  mainVesselTypeId: UUIDSchema.optional(),
  name: z.string().min(2).max(100).regex(VesselTypeR),
});
export type SubVesselTypeFetchsertI = z.infer<typeof SubVesselTypeFetchsertSchema>;

export const SubVesselTypeNestedClientSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});
export type SubVesselTypeNestedClientI = z.infer<typeof SubVesselTypeNestedClientSchema>;


