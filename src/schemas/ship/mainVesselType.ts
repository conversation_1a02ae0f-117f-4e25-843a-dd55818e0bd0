import { DBDataTypeE } from '@consts/common/data';
import { VesselTypeR } from '@consts/common/regex/regex';
import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const MainVesselTypeFetchForClientSchema = PaginationSchema.merge(
  z.object({
    search: z.string().trim().toLowerCase().min(1).max(100).regex(VesselTypeR),
  }),
);
export type MainVesselTypeFetchForClientI = z.infer<typeof MainVesselTypeFetchForClientSchema>;

export const MainVesselTypeFetchsertSchema = z.object({
  name: z.string().min(2).max(100).regex(VesselTypeR),
});
export type MainVesselTypeFetchsertI = z.infer<typeof MainVesselTypeFetchsertSchema>;


export const MainVesselTypeIdClientSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});
export type MainVesselTypeIdClientI = z.infer<typeof MainVesselTypeIdClientSchema>;
