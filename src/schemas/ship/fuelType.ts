import { DBDataTypeE } from '@consts/common/data';
import { FuelTypeR } from '@consts/common/regex/regex';
import { PaginationSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const FuelTypeFetchForClientSchema = PaginationSchema.merge(
  z.object({
    search: z
      .string()
      .min(1)
      .max(100)
      .regex(FuelTypeR)
      .transform((data) => data.trim().toLowerCase()),
  }),
);
export type FuelTypeFetchForClientI = z.infer<typeof FuelTypeFetchForClientSchema>;

export const FuelTypeFetchsertSchema = z.object({
  name: z.string().min(2).max(100).regex(FuelTypeR),
});
export type FuelTypeFetchsertI = z.infer<typeof FuelTypeFetchsertSchema>;

export const FuelTypeIdClientSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});
export type FuelTypeIdClientI = z.infer<typeof FuelTypeIdClientSchema>;
