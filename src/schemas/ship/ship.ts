import { DBDataTypeE } from '@consts/common/data';
import { ShipCallSignR, ShipImoR, ShipMmsiR, ShipR } from '@consts/common/regex/regex';
import { ServiceStatusE } from '@consts/ship/ship';
import { CountryIso2Schema, PaginationSchema, UUIDSchema, YearSchema } from '@schemas/common/common';
import { z } from 'zod';
import { SubVesselTypeNestedClientSchema } from './subVesselType';
import { MainVesselTypeIdClientSchema } from './mainVesselType';

export const ShipImoSchema = z.string().length(7).regex(ShipImoR);
export type ShipImoI = z.infer<typeof ShipImoSchema>;
export const ShipMmsiSchema = z.string().length(9).regex(ShipMmsiR);
export const ShipCallSignSchema = z.string().length(9).regex(ShipCallSignR);

export const ShipNameSchema = z.string().min(1).max(150).regex(ShipR);

export const ShipImoClientSchema = z.object({
  imo: ShipImoSchema,
  dataType: DBDataTypeE,
});

export type ShipImoClientI = z.infer<typeof ShipImoClientSchema>;

export const ShipFetchsertParamsSchema = z.object({
  imo: ShipImoSchema,
  name: ShipNameSchema,
  mmsi: ShipMmsiSchema.optional(),
  callSign: ShipCallSignSchema.optional(),
  flagCountryIso2: CountryIso2Schema.optional(),
  mainVesselType: MainVesselTypeIdClientSchema.optional(),
  subVesselType: (SubVesselTypeNestedClientSchema).optional(),
  status: ServiceStatusE,
  portOfRegistry: z.string().max(255).optional(),
  yearBuilt: YearSchema.optional(),
});
export type ShipFetchsertParamsI = z.infer<typeof ShipFetchsertParamsSchema>;

export const ShipFetchForClientSchema = PaginationSchema.merge(
  z.object({
    search: z.string().min(1).max(100),
  }),
);
export type ShipFetchForClientI = z.infer<typeof ShipFetchForClientSchema>;

export const ShipSearchSchema = PaginationSchema.extend({
  search: z
    .string()
    .min(1)
    .max(100)
    .transform((input) => input?.trim()?.toLowerCase()),
});

export type ShipSearchI = z.infer<typeof ShipSearchSchema>;

export const ShipVisitorFetchForClientParamsSchema = PaginationSchema.merge(ShipImoClientSchema);
export type ShipVisitorFetchForClientParamsI = z.infer<typeof ShipVisitorFetchForClientParamsSchema>;
