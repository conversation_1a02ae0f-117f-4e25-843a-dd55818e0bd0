import { z } from 'zod';

export type ACLI = 'public-read';

export const ParentFolderE = z.enum(['POST', 'AVATAR', 'SHIP', 'PORT', 'CERTIFICATION', 'DOCUMENTATION']);
export type ParentFolderI = z.infer<typeof ParentFolderE>;

export type FileTypeValueI = {
  mime: string;
  extension: string;
  maxSize: number;
};
// uc
export const FileExtensionE = z.enum(['png', 'jpeg', 'jpg', 'webp', 'pdf']);
export type FileExtensionI = z.infer<typeof FileExtensionE>;
export const FileTypeE: Record<FileExtensionI, FileTypeValueI> = {
  png: {
    mime: 'image/png',
    extension: 'png',
    // 500 KB in bytes
    maxSize: 500 * 1024,
  },
  jpeg: {
    mime: 'image/jpeg',
    extension: 'jpeg',
    // 500 KB in bytes
    maxSize: 500 * 1024,
  },
  jpg: {
    mime: 'image/jpg',
    extension: 'jpg',
    // 500 KB in bytes
    maxSize: 500 * 1024,
  },
  webp: {
    mime: 'image/webp',
    extension: 'webp',
    // 500 KB in bytes
    maxSize: 500 * 1024,
  },
  pdf: {
    mime: 'application/pdf',
    extension: 'pdf',
    // 4 MB in bytes
    maxSize: 4 * 1024 * 1024,
  },
};
// 4 MB in bytes
export const MAX_FILE_SIZE: number = 4 * 1024 * 1024;
export const MAX_NO_OF_FILES = 8;

export const DocumentAllowedMimeTypeE = z.enum([
  FileTypeE.jpeg.mime,
  FileTypeE.jpg.mime,
  FileTypeE.webp.mime,
  FileTypeE.pdf.mime,
]);

export const ImageAllowedMimeTypeE = z.enum([FileTypeE.jpeg.mime, FileTypeE.jpg.mime, FileTypeE.webp.mime]);

export const AllowedMimeTypeE = DocumentAllowedMimeTypeE.or(ImageAllowedMimeTypeE);
