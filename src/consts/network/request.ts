import { z } from 'zod';
import { RequestStatusE as RequestStatus } from '@prisma/postgres';

export const RequestStatusTypeE = z.enum([
  RequestStatus.PENDING,
  RequestStatus.ACCEPTED,
  RequestStatus.REJECTED,
  RequestStatus.REVOKED,
  RequestStatus.DISCONNECTED,
]);
export type RequestStatusTypeI = z.infer<typeof RequestStatusTypeE>;

export const RequestTypeE = z.enum(['SENT', 'RECEIVED']);
export type RequestTypeI = z.infer<typeof RequestTypeE>;
