import { EquipmentCategoryPowerCapacityUnitE as EquipmentCategoryPowerCapacityUnit } from '@prisma/postgres';
import { z } from 'zod';

export const EquipmentCategoryPowerCapacityUnitE = z.enum([
  EquipmentCategoryPowerCapacityUnit.CUBIC_METRE,
  EquipmentCategoryPowerCapacityUnit.CUBIC_METRE_PER_HOUR,
  EquipmentCategoryPowerCapacityUnit.KILO_WATT,
  EquipmentCategoryPowerCapacityUnit.LITRE,
  EquipmentCategoryPowerCapacityUnit.LITRES_PER_HOUR,
  EquipmentCategoryPowerCapacityUnit.TONNES,
  EquipmentCategoryPowerCapacityUnit.TONNES_PER_HOUR,
]);
export type EquipmentCategoryPowerCapacityUnitI = z.infer<typeof EquipmentCategoryPowerCapacityUnitE>;
