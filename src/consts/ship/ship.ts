import { ServiceStatusE as ServiceStatus, ShipCapacityUnitTypeE as ShipCapacityUnitType } from '@prisma/postgres';
import {} from '@prisma/postgres';
import { z } from 'zod';

export const ServiceStatusE = z.enum([ServiceStatus.ACTIVE, ServiceStatus.DETAINED, ServiceStatus.SCRAPPED]);
export type ServiceStatusI = z.infer<typeof ServiceStatusE>;

export const ShipCapacityUnitTypeE = z.enum([
  ShipCapacityUnitType.DEADWEIGHT_TONNAGE,
  ShipCapacityUnitType.NOS,
  ShipCapacityUnitType.LITRE,
  ShipCapacityUnitType.LITRES_PER_HOUR,
  ShipCapacityUnitType.CUBIC_METRE,
  ShipCapacityUnitType.CUBIC_METRE_PER_HOUR,
  ShipCapacityUnitType.TONNES,
  ShipCapacityUnitType.TONNES_PER_HOUR,
]);

export type ShipCapacityUnitTypeI = z.infer<typeof ShipCapacityUnitTypeE>;
