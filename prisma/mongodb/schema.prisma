generator client {
  provider = "prisma-client-js"
  output   = "../../node_modules/@prisma/mongodb"
}

datasource db {
  provider = "mongodb"
  url      = env("MONGO_DATABASE_URL")
}

//#region appConfig
enum ModuleE {
  AUTH
}

enum SubModuleE {
  SESSION
}

model AppConfig {
  id        String     @map("_id") @db.ObjectId
  module    ModuleE
  subModule SubModuleE
  config    Json

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([id])
  @@unique([module, subModule])
}

//#endregion appConfig

//#region notification
enum NotificationTypeE {
  PUBLIC
  MESSAGE
  LIKE
  COMMENT
  REPLY
  FOLLOWER
  REQUEST_RECEIVED
  REQUEST_ACCEPTED
  UPDATE_READ
}

model NotificationTemplate {
  id      String            @map("_id") @db.ObjectId
  content Json
  type    NotificationTypeE

  createdAt    DateTime       @default(now()) @db.Timestamp
  updatedAt    DateTime       @updatedAt @db.Timestamp
  Notification Notification[]

  @@id([id])
}

enum NotificationStatusE {
  PENDING
  PARTIAL_SUCCESS
  SUCCESS
  FAILED
}

model Notification {
  id                String              @default(auto()) @map("_id") @db.ObjectId
  type              NotificationTypeE
  variables         Json
  templateId        String              @db.ObjectId
  receiverProfileId String
  status            NotificationStatusE @default(PENDING)
  isRead            Boolean             @default(false)

  createdAt DateTime @default(now()) @db.Timestamp
  updatedAt DateTime @updatedAt @db.Timestamp

  NotificationTemplate NotificationTemplate @relation(fields: [templateId], references: [id])

  @@id([id])
  @@index([type])
  @@index([receiverProfileId])
}

//#endregion notification

//#region session
model Session {
  id          String  @default(auto()) @map("_id") @db.ObjectId
  sessionId   String
  profileId   String
  isActive    Boolean @default(true)
  deviceToken String

  createdAt DateTime @default(now()) @db.Timestamp
  updatedAt DateTime @updatedAt @db.Timestamp

  @@id([id])
  @@unique([sessionId])
  @@index([profileId, isActive])
}

//#endregion session

//#region vendor
enum VendorNamesE {
  FIREBASE
  GOOGLE
}

enum VendorTypesE {
  FCM
  GOOGLE_SERVICE
}

model Vendor {
  id          String       @map("_id") @db.ObjectId
  name        VendorNamesE // unique creates an index
  type        VendorTypesE
  config      Json
  credsConfig String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  @@id([id])
  @@unique([name])
  @@index([type])
}

//#endregion vendor
