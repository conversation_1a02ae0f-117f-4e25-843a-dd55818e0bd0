import { prismaMG } from '../../../src/config/db';
import { SeedMetaItem } from './types';

export const seedMeta: SeedMetaItem<typeof prismaMG.appConfig | typeof prismaMG.vendor | typeof prismaMG.notificationTemplate>[] = [
  {
    table: prismaMG.appConfig,
    fileName: 'appConfig.json',
    id: ['id'],
  },
  {
    table: prismaMG.vendor,
    fileName: 'vendor.json',
    id: ['id'],
  },
  {
    table: prismaMG.notificationTemplate,
    fileName: 'notificationTemplate.json',
    id: ['id'],
  },
];
