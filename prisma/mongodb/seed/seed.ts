import * as fs from 'fs';
import { seedMeta } from './common';
import { prismaMG } from '../../../src/config/db';

const main = async () => {
  for (const seedMetaItem of seedMeta) {
    const data = JSON.parse(fs.readFileSync(`prisma/mongodb/data/${seedMetaItem.fileName}`, 'utf-8'));

    for (const item of data) {
      const where: Record<string, unknown> = seedMetaItem.id.reduce(
        (acc, idField) => {
          acc[idField] = item[idField];
          return acc;
        },
        {} as Record<string, unknown>,
      );

      const existing = await (
        seedMetaItem.table as unknown as {
          findFirst: (args: { where: Record<string, unknown> }) => Promise<unknown | null>;
          update: (args: { where: Record<string, unknown>; data: typeof item }) => Promise<void>;
          create: (args: { data: typeof item }) => Promise<void>;
        }
      ).findFirst({ where });

      const { id: _id, ...dataWithoutId } = item;
      if (existing) {
        await (
          seedMetaItem.table as unknown as {
            update: (args: { where: Record<string, unknown>; data: typeof item }) => Promise<void>;
          }
        ).update({ where, data: { ...dataWithoutId } });
      } else {
        await (
          seedMetaItem.table as unknown as {
            create: (args: { data: typeof item }) => Promise<void>;
          }
        ).create({ data: { ...item } });
      }
    }

    console.log(`✅ Seeded ${seedMetaItem.fileName}`);
  }
};

(async () => {
  try {
    await main();
  } catch (error) {
    console.error('❌ Seeding Error:', error);
    process.exit(1);
  } finally {
    await prismaMG.$disconnect();
  }
})();
