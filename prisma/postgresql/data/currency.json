[{"countryIso2": "AD", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AE", "code": "AED", "name": "UAE Dirham", "symbol": "Ø¯.Ø¥", "numeric": 784, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AF", "code": "AFN", "name": "Afghani", "symbol": "Ø‹", "numeric": 971, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AG", "code": "XCD", "name": "Eastern Caribbean Dollar", "symbol": "EC$", "numeric": 951, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AI", "code": "XCD", "name": "Eastern Caribbean Dollar", "symbol": "EC$", "numeric": 951, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AL", "code": "ALL", "name": "Lek", "symbol": "L", "numeric": 8, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AM", "code": "AMD", "name": "Armenian Dram", "symbol": "Õ¤Ö€.", "numeric": 51, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AO", "code": "AOA", "name": "Kwan<PERSON>", "symbol": "Kz", "numeric": 973, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AQ", "code": "AQD", "name": "Antarctican dollar", "symbol": "AQD$", "numeric": 999, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AR", "code": "ARS", "name": "Argentine Peso", "symbol": "$", "numeric": 32, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AS", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AT", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AU", "code": "AUD", "name": "Australian Dollar", "symbol": "AU$", "numeric": 36, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AW", "code": "AWG", "name": "Aruban Florin", "symbol": "ƒ", "numeric": 533, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AX", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "AZ", "code": "AZN", "name": "Azerbaijan Manat", "symbol": "m", "numeric": 944, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BA", "code": "BAM", "name": "Convertible Mark", "symbol": "ÐšÐœ", "numeric": 977, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BB", "code": "BBD", "name": "Barbados Dollar", "symbol": "$", "numeric": 52, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BD", "code": "BDT", "name": "<PERSON><PERSON>", "symbol": "à§³", "numeric": 50, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BE", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BF", "code": "XOF", "name": "West African CFA franc", "symbol": "CFA", "numeric": 952, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BG", "code": "BGN", "name": "Bulgarian Lev", "symbol": "Ð»Ð²", "numeric": 975, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BH", "code": "BHD", "name": "<PERSON><PERSON>", "symbol": ".Ø¯.Ø¨", "numeric": 48, "decimal": 3, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BI", "code": "BIF", "name": "Burundi Franc", "symbol": "Fr", "numeric": 108, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BJ", "code": "XOF", "name": "West African CFA franc", "symbol": "CFA", "numeric": 952, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BL", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BM", "code": "BMD", "name": "Bermudian Dollar", "symbol": "$", "numeric": 60, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BN", "code": "BND", "name": "Brunei Dollar", "symbol": "$", "numeric": 96, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BO", "code": "BOB", "name": "Boliviano", "symbol": "Bs.", "numeric": 68, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BQ", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BR", "code": "BRL", "name": "Brazilian Real", "symbol": "R$", "numeric": 986, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BS", "code": "BSD", "name": "Bahamian Dollar", "symbol": "$", "numeric": 44, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BT", "code": "BTN", "name": "Bhutanese Ngultrum", "symbol": "<PERSON>u", "numeric": 64, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BV", "code": "NOK", "name": "Norwegian Krone", "symbol": "kr", "numeric": 578, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BW", "code": "BWP", "name": "<PERSON><PERSON>", "symbol": "P", "numeric": 72, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BY", "code": "BYN", "name": "Belarusian Ruble", "symbol": "Br", "numeric": 933, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "BZ", "code": "BZD", "name": "Belize Dollar", "symbol": "$", "numeric": 84, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CA", "code": "CAD", "name": "Canadian Dollar", "symbol": "$", "numeric": 124, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CC", "code": "AUD", "name": "Australian Dollar", "symbol": "AU$", "numeric": 36, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CD", "code": "CDF", "name": "Congolese Franc", "symbol": "Fr", "numeric": 976, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CF", "code": "XAF", "name": "Central African CFA franc", "symbol": "FCFA", "numeric": 950, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CG", "code": "XAF", "name": "Central African CFA franc", "symbol": "FCFA", "numeric": 950, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CH", "code": "CHF", "name": "Swiss Franc", "symbol": "CHF", "numeric": 756, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CI", "code": "XOF", "name": "West African CFA franc", "symbol": "CFA", "numeric": 952, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CK", "code": "NZD", "name": "New Zealand Dollar", "symbol": "NZ$", "numeric": 554, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CL", "code": "CLP", "name": "Chilean Peso", "symbol": "$", "numeric": 152, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CM", "code": "XAF", "name": "Central African CFA franc", "symbol": "FCFA", "numeric": 950, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CN", "code": "CNY", "name": "<PERSON>", "symbol": "¥", "numeric": 156, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CO", "code": "COP", "name": "Colombian Peso", "symbol": "$", "numeric": 170, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CR", "code": "CRC", "name": "Costa Rican Colon", "symbol": "â‚¡", "numeric": 188, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CU", "code": "CUP", "name": "Cuban Peso", "symbol": "$", "numeric": 192, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CV", "code": "CVE", "name": "Cabo Verde Escudo", "symbol": "Esc or $", "numeric": 132, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CW", "code": "ANG", "name": "Netherlands Antillean Guilder", "symbol": "NAƒ", "numeric": 532, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CX", "code": "AUD", "name": "Australian Dollar", "symbol": "AU$", "numeric": 36, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CY", "code": "CYP", "name": "Cypriot Pound", "symbol": "£", "numeric": 196, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "CZ", "code": "CZK", "name": "Czech Koruna", "symbol": "KÄ", "numeric": 203, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "DE", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "DJ", "code": "DJF", "name": "Djibouti Franc", "symbol": "Fr", "numeric": 262, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "DK", "code": "DKK", "name": "Danish Krone", "symbol": "kr", "numeric": 208, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "DM", "code": "XCD", "name": "Eastern Caribbean Dollar", "symbol": "EC$", "numeric": 951, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "DO", "code": "DOP", "name": "Dominican Peso", "symbol": "$", "numeric": 214, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "DZ", "code": "DZD", "name": "Algerian Dinar", "symbol": "Ø¯.Ø¬", "numeric": 12, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "EC", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "EE", "code": "EEK", "name": "Estonian Kroon", "symbol": "kr", "numeric": 233, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "EG", "code": "EGP", "name": "Egyptian Pound", "symbol": "Ø¬.Ù…", "numeric": 818, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "EH", "code": "MAD", "name": "Moroccan <PERSON><PERSON><PERSON>", "symbol": "MAD", "numeric": 504, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "ER", "code": "ERN", "name": "Eritrean Nakfa", "symbol": "Nfk", "numeric": 232, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "ES", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "ET", "code": "ETB", "name": "Ethiopian Birr", "symbol": "Br", "numeric": 230, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "FI", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "FJ", "code": "FJD", "name": "Fiji Dollar", "symbol": "$", "numeric": 242, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "FK", "code": "FKP", "name": "Falkland Islands Pound", "symbol": "£", "numeric": 238, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "FM", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "FO", "code": "DKK", "name": "Danish Krone", "symbol": "kr", "numeric": 208, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "FR", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GA", "code": "XAF", "name": "Central African CFA franc", "symbol": "FCFA", "numeric": 950, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GB", "code": "GBP", "name": "Pound Sterling", "symbol": "£", "numeric": 826, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GD", "code": "XCD", "name": "Eastern Caribbean Dollar", "symbol": "EC$", "numeric": 951, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GE", "code": "GEL", "name": "<PERSON><PERSON>", "symbol": "<PERSON><PERSON><PERSON>", "numeric": 981, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GF", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GG", "code": "GGP", "name": "Guernsey pound", "symbol": "£", "numeric": 831, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GH", "code": "GHS", "name": "Ghana Cedi", "symbol": "â‚µ", "numeric": 936, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GI", "code": "GIP", "name": "Gibraltar Pound", "symbol": "£", "numeric": 292, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GL", "code": "DKK", "name": "Danish Krone", "symbol": "kr", "numeric": 208, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GM", "code": "GMD", "name": "<PERSON><PERSON>", "symbol": "D", "numeric": 270, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GN", "code": "GNF", "name": "Guinean Franc", "symbol": "Fr", "numeric": 324, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GP", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GQ", "code": "XAF", "name": "Central African CFA franc", "symbol": "FCFA", "numeric": 950, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GR", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GS", "code": "GBP", "name": "Pound Sterling", "symbol": "£", "numeric": 826, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GT", "code": "GTQ", "name": "Quetzal", "symbol": "Q", "numeric": 320, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GU", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GW", "code": "XOF", "name": "West African CFA franc", "symbol": "CFA", "numeric": 952, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "GY", "code": "GYD", "name": "Guyana Dollar", "symbol": "$", "numeric": 328, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "HK", "code": "HKD", "name": "Hong Kong Dollar", "symbol": "$", "numeric": 344, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "HM", "code": "AUD", "name": "Australian Dollar", "symbol": "AU$", "numeric": 36, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "HN", "code": "HNL", "name": "<PERSON><PERSON><PERSON>", "symbol": "L", "numeric": 340, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "HR", "code": "HRK", "name": "<PERSON><PERSON>", "symbol": "kn", "numeric": 191, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "HT", "code": "HTG", "name": "<PERSON><PERSON><PERSON>", "symbol": "G", "numeric": 332, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "HU", "code": "HUF", "name": "Forint", "symbol": "Ft", "numeric": 348, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "ID", "code": "IDR", "name": "<PERSON><PERSON><PERSON>", "symbol": "Rp", "numeric": 360, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "IE", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "IL", "code": "ILS", "name": "New Israeli Sheqel", "symbol": "â‚ª", "numeric": 376, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "IM", "code": "GBP", "name": "Pound Sterling", "symbol": "£", "numeric": 826, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "IN", "code": "INR", "name": "Indian Rupee", "symbol": "â‚¹", "numeric": 356, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "IO", "code": "GBP", "name": "Pound Sterling", "symbol": "£", "numeric": 826, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "IQ", "code": "IQD", "name": "Iraqi <PERSON>", "symbol": "Ø¹.Ø¯", "numeric": 368, "decimal": 3, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "IR", "code": "IRR", "name": "Iranian Rial", "symbol": "ï·¼", "numeric": 364, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "IS", "code": "ISK", "name": "Iceland Krona", "symbol": "kr", "numeric": 352, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "IT", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "JE", "code": "GBP", "name": "Pound Sterling", "symbol": "£", "numeric": 826, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "JM", "code": "JMD", "name": "Jamaican Dollar", "symbol": "$", "numeric": 388, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "JO", "code": "JOD", "name": "<PERSON><PERSON>", "symbol": "د.ا", "numeric": 400, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "JP", "code": "JPY", "name": "Yen", "symbol": "¥", "numeric": 392, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "KE", "code": "KES", "name": "Kenyan Shilling", "symbol": "Sh", "numeric": 404, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "KG", "code": "KGS", "name": "Som", "symbol": "Ð»Ð²", "numeric": 417, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "KH", "code": "KHR", "name": "Riel", "symbol": "áŸ›", "numeric": 116, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "KI", "code": "AUD", "name": "Australian Dollar", "symbol": "AU$", "numeric": 36, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "KM", "code": "KMF", "name": "<PERSON><PERSON> ", "symbol": "Fr", "numeric": 174, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "KN", "code": "XCD", "name": "Eastern Caribbean Dollar", "symbol": "EC$", "numeric": 951, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "KP", "code": "KPW", "name": "North Korean Won", "symbol": "â‚©", "numeric": 408, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "KR", "code": "KRW", "name": "Won", "symbol": "â‚©", "numeric": 410, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "KW", "code": "KWD", "name": "<PERSON><PERSON>", "symbol": "Ø¯.Ùƒ", "numeric": 414, "decimal": 3, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "KY", "code": "KYD", "name": "Cayman Islands Dollar", "symbol": "$", "numeric": 136, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "KZ", "code": "KZT", "name": "Tenge", "symbol": "â‚¸", "numeric": 398, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "LA", "code": "LAK", "name": "<PERSON>", "symbol": "â‚­", "numeric": 418, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "LB", "code": "LBP", "name": "Lebanese Pound", "symbol": "Ù„.Ù„", "numeric": 422, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "LC", "code": "XCD", "name": "Eastern Caribbean Dollar", "symbol": "EC$", "numeric": 951, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "LI", "code": "CHF", "name": "Swiss Franc", "symbol": "Fr", "numeric": 756, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "LK", "code": "LKR", "name": "Sri Lanka Rupee", "symbol": "Rs", "numeric": 144, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "LR", "code": "LRD", "name": "Liberian Dollar", "symbol": "$", "numeric": 430, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "LS", "code": "LSL", "name": "Loti", "symbol": "L", "numeric": 426, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "LT", "code": "LTL", "name": "Lita", "symbol": "Lt", "numeric": 440, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "LU", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "LV", "code": "LVL", "name": "Lat", "symbol": "Ls", "numeric": 428, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "LY", "code": "LYD", "name": "Libyan Dinar", "symbol": "Ù„.Ø¯", "numeric": 434, "decimal": 3, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MA", "code": "MAD", "name": "Moroccan <PERSON><PERSON><PERSON>", "symbol": "Ø¯.Ù….", "numeric": 504, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MC", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MD", "code": "MDL", "name": "Moldovan Leu", "symbol": "L", "numeric": 498, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "ME", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MF", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MG", "code": "MGA", "name": "Malagasy Ariary", "symbol": "Ar", "numeric": 969, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MH", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MK", "code": "MKD", "name": "<PERSON><PERSON>", "symbol": "Ð´ÐµÐ½", "numeric": 807, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "ML", "code": "XOF", "name": "West African CFA franc", "symbol": "CFA", "numeric": 952, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MM", "code": "MMK", "name": "Kyat", "symbol": "Ks", "numeric": 104, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MN", "code": "MNT", "name": "<PERSON><PERSON><PERSON>", "symbol": "â‚®", "numeric": 496, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MO", "code": "MOP", "name": "Pataca", "symbol": "P", "numeric": 446, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MP", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MQ", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MR", "code": "MRO", "name": "Ouguiya", "symbol": "UM", "numeric": 462, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MS", "code": "XCD", "name": "Eastern Caribbean Dollar", "symbol": "EC$", "numeric": 951, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MT", "code": "MTL", "name": "Maltese Lira", "symbol": "Lm", "numeric": 470, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MU", "code": "MUR", "name": "Mauritius Rupee", "symbol": "â‚¨", "numeric": 480, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MV", "code": "MVR", "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": ".<PERSON><PERSON>", "numeric": 462, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MW", "code": "MWK", "name": "Malawi Kwacha", "symbol": "MK", "numeric": 454, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MX", "code": "MXN", "name": "Mexican Peso", "symbol": "$", "numeric": 484, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MY", "code": "MYR", "name": "Malaysian Ringgit", "symbol": "RM", "numeric": 458, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "MZ", "code": "MZN", "name": "Mozambique Metical", "symbol": "MT", "numeric": 943, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NA", "code": "NAD", "name": "Namibia Dollar", "symbol": "$", "numeric": 516, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NC", "code": "XPF", "name": "CFP Franc", "symbol": "Fr", "numeric": 953, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NE", "code": "XOF", "name": "West African CFA franc", "symbol": "CFA", "numeric": 952, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NF", "code": "AUD", "name": "Australian Dollar", "symbol": "AU$", "numeric": 36, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NG", "code": "NGN", "name": "<PERSON><PERSON>", "symbol": "â‚¦", "numeric": 566, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NI", "code": "NIO", "name": "Cordoba Oro", "symbol": "C$", "numeric": 558, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NL", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NO", "code": "NOK", "name": "Norwegian krone", "symbol": "kr", "numeric": 578, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NP", "code": "NPR", "name": "Nepalese Rupee", "symbol": "â‚¨", "numeric": 524, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NR", "code": "AUD", "name": "Australian Dollar", "symbol": "AU$", "numeric": 36, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NU", "code": "NZD", "name": "New Zealand Dollar", "symbol": "NZ$", "numeric": 554, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "NZ", "code": "NZD", "name": "New Zealand Dollar", "symbol": "NZ$", "numeric": 554, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "OM", "code": "OMR", "name": "<PERSON><PERSON>", "symbol": "Ø±.Ø¹.", "numeric": 512, "decimal": 3, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PA", "code": "PAB", "name": "Balboa", "symbol": "B/.", "numeric": 590, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PE", "code": "PEN", "name": "Sol", "symbol": "S/.", "numeric": 604, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PF", "code": "XPF", "name": "CFP Franc", "symbol": "Fr", "numeric": 953, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PG", "code": "PGK", "name": "<PERSON><PERSON>", "symbol": "K", "numeric": 598, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PH", "code": "PHP", "name": "Philippine Peso", "symbol": "â‚±", "numeric": 608, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PK", "code": "PKR", "name": "Pakistan Rupee", "symbol": "â‚¨", "numeric": 586, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PL", "code": "PLN", "name": "<PERSON><PERSON><PERSON>", "symbol": "zÅ‚", "numeric": 985, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PM", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PN", "code": "NZD", "name": "New Zealand Dollar", "symbol": "NZ$", "numeric": 554, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PR", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PS", "code": "JOD", "name": "<PERSON><PERSON>", "symbol": "Ø¯.Ø§", "numeric": 400, "decimal": 3, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PT", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PW", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "PY", "code": "PYG", "name": "Guarani", "symbol": "â‚²", "numeric": 600, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "QA", "code": "QAR", "name": "<PERSON><PERSON> R<PERSON>", "symbol": "Ø±.Ù‚", "numeric": 634, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "RE", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "RO", "code": "RON", "name": "Romanian Leu", "symbol": "L", "numeric": 946, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "RS", "code": "RSD", "name": "Serbian Dinar", "symbol": "Ð´Ð¸Ð½.", "numeric": 941, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "RU", "code": "RUB", "name": "Russian Ruble", "symbol": "Ñ€ÑƒÐ±.", "numeric": 643, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "RW", "code": "RWF", "name": "Rwanda Franc", "symbol": "Fr", "numeric": 646, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SA", "code": "SAR", "name": "Saudi Riyal", "symbol": "Ø±.Ø³", "numeric": 682, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SB", "code": "SBD", "name": "Solomon Islands Dollar", "symbol": "$", "numeric": 90, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SC", "code": "SCR", "name": "Seychelles Rupee", "symbol": "â‚¨", "numeric": 690, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SD", "code": "SDG", "name": "Sudanese Pound", "symbol": "£", "numeric": 938, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SE", "code": "SEK", "name": "Swedish Krona", "symbol": "kr", "numeric": 752, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SG", "code": "SGD", "name": "Singapore Dollar", "symbol": "$", "numeric": 702, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SH", "code": "GBP", "name": "Pound Sterling", "symbol": "£", "numeric": 826, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SI", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SJ", "code": "NOK", "name": "Norwegian Krone", "symbol": "kr", "numeric": 578, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SK", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SL", "code": "SLL", "name": "Leone", "symbol": "Le", "numeric": 694, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SM", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SN", "code": "XOF", "name": "West African CFA franc", "symbol": "CFA", "numeric": 952, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SO", "code": "SOS", "name": "Somali Shilling", "symbol": "Sh", "numeric": 706, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SR", "code": "SRD", "name": "Surinam Dollar", "symbol": "$", "numeric": 968, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SS", "code": "SSP", "name": "South Sudanese Pound", "symbol": "£", "numeric": 728, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "ST", "code": "STN", "name": "Dobra", "symbol": "Db", "numeric": 930, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SV", "code": "SVC", "name": "El Salvador Colon", "symbol": "$", "numeric": 222, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SX", "code": "ANG", "name": "Netherlands Antillean Guilder", "symbol": "ƒ", "numeric": 532, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SY", "code": "SYP", "name": "Syrian Pound", "symbol": "Ù„.Ø³", "numeric": 760, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "SZ", "code": "SZL", "name": "<PERSON><PERSON><PERSON>", "symbol": "L", "numeric": 748, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TC", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TD", "code": "XAF", "name": "Central African CFA franc", "symbol": "FCFA", "numeric": 950, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TF", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TG", "code": "XOF", "name": "CFA Franc BCEAO", "symbol": "Fr", "numeric": 952, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TH", "code": "THB", "name": "Baht", "symbol": "à¸¿", "numeric": 764, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TJ", "code": "TJS", "name": "So<PERSON><PERSON>", "symbol": "Ð…Ðœ", "numeric": 972, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TK", "code": "NZD", "name": "New Zealand Dollar", "symbol": "$", "numeric": 554, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TL", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TM", "code": "TMT", "name": "Turkmenistan New Manat", "symbol": "m", "numeric": 934, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TN", "code": "TND", "name": "Tunisian Dinar", "symbol": "Ø¯.Øª", "numeric": 788, "decimal": 3, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TO", "code": "TOP", "name": "Pa’anga", "symbol": "T$", "numeric": 776, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TR", "code": "TRY", "name": "Turkish Lira", "symbol": "â‚º", "numeric": 949, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TT", "code": "TTD", "name": "Trinidad and Tobago Dollar", "symbol": "$", "numeric": 780, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TV", "code": "AUD", "name": "Australian Dollar", "symbol": "$", "numeric": 36, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TW", "code": "TWD", "name": "New Taiwan Dollar", "symbol": "$", "numeric": 901, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "TZ", "code": "TZS", "name": "Tanzanian <PERSON>", "symbol": "Sh", "numeric": 834, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "UA", "code": "UAH", "name": "Hryvnia", "symbol": "â‚´", "numeric": 980, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "UG", "code": "UGX", "name": "Uganda Shilling", "symbol": "Sh", "numeric": 800, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "UM", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "US", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "UY", "code": "UYU", "name": "Peso Uruguayo", "symbol": "$", "numeric": 858, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "UZ", "code": "UZS", "name": "Uzbekistan Sum", "symbol": "Ð»Ð²", "numeric": 860, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "VA", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "VC", "code": "XCD", "name": "East Caribbean Dollar", "symbol": "$", "numeric": 951, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "VE", "code": "VES", "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "Bs", "numeric": 928, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "VG", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "VI", "code": "USD", "name": "US Dollar", "symbol": "$", "numeric": 840, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "VN", "code": "VND", "name": "<PERSON>", "symbol": "â‚«", "numeric": 704, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "VU", "code": "VUV", "name": "Vatu", "symbol": "Vt", "numeric": 548, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "WF", "code": "XPF", "name": "CFP Franc", "symbol": "Fr", "numeric": 953, "decimal": 0, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "WS", "code": "WST", "name": "Samoan <PERSON>", "symbol": "WS$", "numeric": 882, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "XK", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "YE", "code": "YER", "name": "Yemeni R<PERSON>", "symbol": "ï·¼", "numeric": 886, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "YT", "code": "EUR", "name": "Euro", "symbol": "€", "numeric": 978, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "ZA", "code": "ZAR", "name": "Rand", "symbol": "R", "numeric": 710, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "ZM", "code": "ZMW", "name": "Zambian <PERSON>", "symbol": "ZK", "numeric": 967, "decimal": 2, "createdAt": "2025-04-11T11:43:09.583Z", "updatedAt": "2025-04-11T11:43:09.583Z"}, {"countryIso2": "ZW", "code": "ZWL", "name": "Zimbabwe Dollar", "symbol": "Z$", "numeric": 932, "decimal": 2}]